﻿#pragma checksum "..\..\..\..\Views\AdvancedConfigWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "DD57F1B34F28B2B8C09D40154733EB9B1A2742FD"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace OneClickWorkTool.Views {
    
    
    /// <summary>
    /// AdvancedConfigWindow
    /// </summary>
    public partial class AdvancedConfigWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 40 "..\..\..\..\Views\AdvancedConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox UseCustomConfigCheckBox;
        
        #line default
        #line hidden
        
        
        #line 48 "..\..\..\..\Views\AdvancedConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid CustomConfigGrid;
        
        #line default
        #line hidden
        
        
        #line 58 "..\..\..\..\Views\AdvancedConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox CustomConfigListBox;
        
        #line default
        #line hidden
        
        
        #line 60 "..\..\..\..\Views\AdvancedConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NewConfigTextBox;
        
        #line default
        #line hidden
        
        
        #line 62 "..\..\..\..\Views\AdvancedConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddConfigButton;
        
        #line default
        #line hidden
        
        
        #line 64 "..\..\..\..\Views\AdvancedConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RemoveConfigButton;
        
        #line default
        #line hidden
        
        
        #line 72 "..\..\..\..\Views\AdvancedConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox CustomPlatformListBox;
        
        #line default
        #line hidden
        
        
        #line 74 "..\..\..\..\Views\AdvancedConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NewPlatformTextBox;
        
        #line default
        #line hidden
        
        
        #line 76 "..\..\..\..\Views\AdvancedConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddPlatformButton;
        
        #line default
        #line hidden
        
        
        #line 78 "..\..\..\..\Views\AdvancedConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RemovePlatformButton;
        
        #line default
        #line hidden
        
        
        #line 85 "..\..\..\..\Views\AdvancedConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel AutoConfigPanel;
        
        #line default
        #line hidden
        
        
        #line 96 "..\..\..\..\Views\AdvancedConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox DetectedConfigListBox;
        
        #line default
        #line hidden
        
        
        #line 101 "..\..\..\..\Views\AdvancedConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox DetectedPlatformListBox;
        
        #line default
        #line hidden
        
        
        #line 105 "..\..\..\..\Views\AdvancedConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshConfigButton;
        
        #line default
        #line hidden
        
        
        #line 128 "..\..\..\..\Views\AdvancedConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid BackendProcessDataGrid;
        
        #line default
        #line hidden
        
        
        #line 142 "..\..\..\..\Views\AdvancedConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddProcessButton;
        
        #line default
        #line hidden
        
        
        #line 144 "..\..\..\..\Views\AdvancedConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button EditProcessButton;
        
        #line default
        #line hidden
        
        
        #line 146 "..\..\..\..\Views\AdvancedConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RemoveProcessButton;
        
        #line default
        #line hidden
        
        
        #line 148 "..\..\..\..\Views\AdvancedConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button MoveUpButton;
        
        #line default
        #line hidden
        
        
        #line 150 "..\..\..\..\Views\AdvancedConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button MoveDownButton;
        
        #line default
        #line hidden
        
        
        #line 159 "..\..\..\..\Views\AdvancedConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OkButton;
        
        #line default
        #line hidden
        
        
        #line 161 "..\..\..\..\Views\AdvancedConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/OneClickWorkTool;V1.0.0.0;component/views/advancedconfigwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\AdvancedConfigWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.UseCustomConfigCheckBox = ((System.Windows.Controls.CheckBox)(target));
            
            #line 41 "..\..\..\..\Views\AdvancedConfigWindow.xaml"
            this.UseCustomConfigCheckBox.Checked += new System.Windows.RoutedEventHandler(this.UseCustomConfigCheckBox_Checked);
            
            #line default
            #line hidden
            
            #line 42 "..\..\..\..\Views\AdvancedConfigWindow.xaml"
            this.UseCustomConfigCheckBox.Unchecked += new System.Windows.RoutedEventHandler(this.UseCustomConfigCheckBox_Unchecked);
            
            #line default
            #line hidden
            return;
            case 2:
            this.CustomConfigGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 3:
            this.CustomConfigListBox = ((System.Windows.Controls.ListBox)(target));
            return;
            case 4:
            this.NewConfigTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 61 "..\..\..\..\Views\AdvancedConfigWindow.xaml"
            this.NewConfigTextBox.KeyDown += new System.Windows.Input.KeyEventHandler(this.NewConfigTextBox_KeyDown);
            
            #line default
            #line hidden
            return;
            case 5:
            this.AddConfigButton = ((System.Windows.Controls.Button)(target));
            
            #line 63 "..\..\..\..\Views\AdvancedConfigWindow.xaml"
            this.AddConfigButton.Click += new System.Windows.RoutedEventHandler(this.AddConfigButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.RemoveConfigButton = ((System.Windows.Controls.Button)(target));
            
            #line 65 "..\..\..\..\Views\AdvancedConfigWindow.xaml"
            this.RemoveConfigButton.Click += new System.Windows.RoutedEventHandler(this.RemoveConfigButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.CustomPlatformListBox = ((System.Windows.Controls.ListBox)(target));
            return;
            case 8:
            this.NewPlatformTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 75 "..\..\..\..\Views\AdvancedConfigWindow.xaml"
            this.NewPlatformTextBox.KeyDown += new System.Windows.Input.KeyEventHandler(this.NewPlatformTextBox_KeyDown);
            
            #line default
            #line hidden
            return;
            case 9:
            this.AddPlatformButton = ((System.Windows.Controls.Button)(target));
            
            #line 77 "..\..\..\..\Views\AdvancedConfigWindow.xaml"
            this.AddPlatformButton.Click += new System.Windows.RoutedEventHandler(this.AddPlatformButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.RemovePlatformButton = ((System.Windows.Controls.Button)(target));
            
            #line 79 "..\..\..\..\Views\AdvancedConfigWindow.xaml"
            this.RemovePlatformButton.Click += new System.Windows.RoutedEventHandler(this.RemovePlatformButton_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.AutoConfigPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 12:
            this.DetectedConfigListBox = ((System.Windows.Controls.ListBox)(target));
            return;
            case 13:
            this.DetectedPlatformListBox = ((System.Windows.Controls.ListBox)(target));
            return;
            case 14:
            this.RefreshConfigButton = ((System.Windows.Controls.Button)(target));
            
            #line 107 "..\..\..\..\Views\AdvancedConfigWindow.xaml"
            this.RefreshConfigButton.Click += new System.Windows.RoutedEventHandler(this.RefreshConfigButton_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.BackendProcessDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 16:
            this.AddProcessButton = ((System.Windows.Controls.Button)(target));
            
            #line 143 "..\..\..\..\Views\AdvancedConfigWindow.xaml"
            this.AddProcessButton.Click += new System.Windows.RoutedEventHandler(this.AddProcessButton_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.EditProcessButton = ((System.Windows.Controls.Button)(target));
            
            #line 145 "..\..\..\..\Views\AdvancedConfigWindow.xaml"
            this.EditProcessButton.Click += new System.Windows.RoutedEventHandler(this.EditProcessButton_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            this.RemoveProcessButton = ((System.Windows.Controls.Button)(target));
            
            #line 147 "..\..\..\..\Views\AdvancedConfigWindow.xaml"
            this.RemoveProcessButton.Click += new System.Windows.RoutedEventHandler(this.RemoveProcessButton_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.MoveUpButton = ((System.Windows.Controls.Button)(target));
            
            #line 149 "..\..\..\..\Views\AdvancedConfigWindow.xaml"
            this.MoveUpButton.Click += new System.Windows.RoutedEventHandler(this.MoveUpButton_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            this.MoveDownButton = ((System.Windows.Controls.Button)(target));
            
            #line 151 "..\..\..\..\Views\AdvancedConfigWindow.xaml"
            this.MoveDownButton.Click += new System.Windows.RoutedEventHandler(this.MoveDownButton_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            this.OkButton = ((System.Windows.Controls.Button)(target));
            
            #line 160 "..\..\..\..\Views\AdvancedConfigWindow.xaml"
            this.OkButton.Click += new System.Windows.RoutedEventHandler(this.OkButton_Click);
            
            #line default
            #line hidden
            return;
            case 22:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 162 "..\..\..\..\Views\AdvancedConfigWindow.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

