using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Windows;
using Microsoft.Win32;
using OneClickWorkTool.Models;

namespace OneClickWorkTool.Views
{
    /// <summary>
    /// ProjectConfigWindow.xaml 的交互逻辑
    /// </summary>
    public partial class ProjectConfigWindow : Window
    {
        public ProjectConfig? ProjectConfig { get; private set; }
        
        private readonly bool _isEditMode;
        private readonly List<string> _targetPaths = new List<string>();

        public ProjectConfigWindow(ProjectConfig? existingProject = null)
        {
            InitializeComponent();
            
            _isEditMode = existingProject != null;
            
            InitializeUI();
            
            if (existingProject != null)
            {
                LoadProjectConfig(existingProject);
                Title = $"编辑项目 - {existingProject.Name}";
            }
            else
            {
                Title = "新建项目";
                ProjectConfig = new ProjectConfig();
            }
        }

        private void InitializeUI()
        {
            // 初始化编译配置选项
            BuildConfigComboBox.ItemsSource = new[] { "Debug", "Release", "RelWithDebInfo", "MinSizeRel" };
            BuildConfigComboBox.SelectedItem = "Release";
            
            BuildPlatformComboBox.ItemsSource = new[] { "Win32", "x64", "Any CPU" };
            BuildPlatformComboBox.SelectedItem = "Win32";
            
            // 绑定目标路径列表
            DataTableTargetPathsListBox.ItemsSource = _targetPaths;
        }

        private void LoadProjectConfig(ProjectConfig project)
        {
            ProjectNameTextBox.Text = project.Name;
            ProjectPathTextBox.Text = project.ProjectPath;
            SolutionPathTextBox.Text = project.SolutionPath;
            FrontendExePathTextBox.Text = project.FrontendExePath;
            FrontendArgsTextBox.Text = project.FrontendStartupArgs;
            BackendExePathTextBox.Text = project.BackendExePath;
            BackendArgsTextBox.Text = project.BackendStartupArgs;
            DataTableSourcePathTextBox.Text = project.DataTableSourcePath;
            PackageOutputPathTextBox.Text = project.PackageOutputPath;
            
            BuildConfigComboBox.SelectedItem = project.BuildConfiguration;
            BuildPlatformComboBox.SelectedItem = project.BuildPlatform;
            
            _targetPaths.Clear();
            _targetPaths.AddRange(project.DataTableTargetPaths);
            DataTableTargetPathsListBox.Items.Refresh();
            
            ProjectConfig = project;
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(ProjectNameTextBox.Text))
            {
                MessageBox.Show("请输入项目名称", "验证错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                ProjectNameTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(ProjectPathTextBox.Text))
            {
                MessageBox.Show("请选择项目路径", "验证错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (!Directory.Exists(ProjectPathTextBox.Text))
            {
                MessageBox.Show("项目路径不存在", "验证错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (!string.IsNullOrWhiteSpace(SolutionPathTextBox.Text) && !File.Exists(SolutionPathTextBox.Text))
            {
                MessageBox.Show("解决方案文件不存在", "验证错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (!string.IsNullOrWhiteSpace(FrontendExePathTextBox.Text) && !File.Exists(FrontendExePathTextBox.Text))
            {
                MessageBox.Show("前端可执行文件不存在", "验证错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (!string.IsNullOrWhiteSpace(BackendExePathTextBox.Text) && !File.Exists(BackendExePathTextBox.Text))
            {
                MessageBox.Show("后端可执行文件不存在", "验证错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            return true;
        }

        private void SaveProjectConfig()
        {
            if (ProjectConfig == null)
                ProjectConfig = new ProjectConfig();

            ProjectConfig.Name = ProjectNameTextBox.Text.Trim();
            ProjectConfig.ProjectPath = ProjectPathTextBox.Text.Trim();
            ProjectConfig.SolutionPath = SolutionPathTextBox.Text.Trim();
            ProjectConfig.FrontendExePath = FrontendExePathTextBox.Text.Trim();
            ProjectConfig.FrontendStartupArgs = FrontendArgsTextBox.Text.Trim();
            ProjectConfig.BackendExePath = BackendExePathTextBox.Text.Trim();
            ProjectConfig.BackendStartupArgs = BackendArgsTextBox.Text.Trim();
            ProjectConfig.DataTableSourcePath = DataTableSourcePathTextBox.Text.Trim();
            ProjectConfig.PackageOutputPath = PackageOutputPathTextBox.Text.Trim();
            
            ProjectConfig.BuildConfiguration = BuildConfigComboBox.SelectedItem?.ToString() ?? "Release";
            ProjectConfig.BuildPlatform = BuildPlatformComboBox.SelectedItem?.ToString() ?? "Win32";
            
            ProjectConfig.DataTableTargetPaths.Clear();
            ProjectConfig.DataTableTargetPaths.AddRange(_targetPaths);
        }

        #region 文件浏览事件

        private void BrowseProjectPathButton_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new System.Windows.Forms.FolderBrowserDialog
            {
                Description = "选择项目根目录",
                ShowNewFolderButton = true
            };

            if (!string.IsNullOrWhiteSpace(ProjectPathTextBox.Text))
            {
                dialog.SelectedPath = ProjectPathTextBox.Text;
            }

            if (dialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                ProjectPathTextBox.Text = dialog.SelectedPath;
            }
        }

        private void BrowseSolutionPathButton_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new OpenFileDialog
            {
                Title = "选择解决方案文件",
                Filter = "解决方案文件 (*.sln)|*.sln|所有文件 (*.*)|*.*",
                CheckFileExists = true
            };

            if (!string.IsNullOrWhiteSpace(ProjectPathTextBox.Text))
            {
                dialog.InitialDirectory = ProjectPathTextBox.Text;
            }

            if (dialog.ShowDialog() == true)
            {
                SolutionPathTextBox.Text = dialog.FileName;
            }
        }

        private void BrowseFrontendExeButton_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new OpenFileDialog
            {
                Title = "选择前端可执行文件",
                Filter = "可执行文件 (*.exe)|*.exe|所有文件 (*.*)|*.*",
                CheckFileExists = true
            };

            if (!string.IsNullOrWhiteSpace(ProjectPathTextBox.Text))
            {
                dialog.InitialDirectory = ProjectPathTextBox.Text;
            }

            if (dialog.ShowDialog() == true)
            {
                FrontendExePathTextBox.Text = dialog.FileName;
            }
        }

        private void BrowseBackendExeButton_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new OpenFileDialog
            {
                Title = "选择后端可执行文件",
                Filter = "可执行文件 (*.exe)|*.exe|所有文件 (*.*)|*.*",
                CheckFileExists = true
            };

            if (!string.IsNullOrWhiteSpace(ProjectPathTextBox.Text))
            {
                dialog.InitialDirectory = ProjectPathTextBox.Text;
            }

            if (dialog.ShowDialog() == true)
            {
                BackendExePathTextBox.Text = dialog.FileName;
            }
        }

        private void BrowseDataTableSourceButton_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new System.Windows.Forms.FolderBrowserDialog
            {
                Description = "选择导表源目录",
                ShowNewFolderButton = false
            };

            if (!string.IsNullOrWhiteSpace(DataTableSourcePathTextBox.Text))
            {
                dialog.SelectedPath = DataTableSourcePathTextBox.Text;
            }

            if (dialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                DataTableSourcePathTextBox.Text = dialog.SelectedPath;
            }
        }

        private void BrowsePackageOutputButton_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new System.Windows.Forms.FolderBrowserDialog
            {
                Description = "选择打包输出目录",
                ShowNewFolderButton = true
            };

            if (!string.IsNullOrWhiteSpace(PackageOutputPathTextBox.Text))
            {
                dialog.SelectedPath = PackageOutputPathTextBox.Text;
            }

            if (dialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                PackageOutputPathTextBox.Text = dialog.SelectedPath;
            }
        }

        #endregion

        #region 目标路径管理

        private void AddTargetPathButton_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new System.Windows.Forms.FolderBrowserDialog
            {
                Description = "选择导表目标目录",
                ShowNewFolderButton = true
            };

            if (dialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                if (!_targetPaths.Contains(dialog.SelectedPath))
                {
                    _targetPaths.Add(dialog.SelectedPath);
                    DataTableTargetPathsListBox.Items.Refresh();
                }
                else
                {
                    MessageBox.Show("该路径已存在", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
        }

        private void RemoveTargetPathButton_Click(object sender, RoutedEventArgs e)
        {
            if (DataTableTargetPathsListBox.SelectedItem is string selectedPath)
            {
                _targetPaths.Remove(selectedPath);
                DataTableTargetPathsListBox.Items.Refresh();
            }
            else
            {
                MessageBox.Show("请先选择要删除的路径", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        #endregion

        #region 按钮事件

        private void OkButton_Click(object sender, RoutedEventArgs e)
        {
            if (ValidateInput())
            {
                SaveProjectConfig();
                DialogResult = true;
                Close();
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        #endregion
    }
}
