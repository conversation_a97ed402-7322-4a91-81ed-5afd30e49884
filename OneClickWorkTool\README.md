# 一键工作工具

## 项目简介

这是一个专为C++游戏开发程序员设计的工作流管理工具，主要用于简化使用Visual Studio 2008进行游戏项目前后端开发的日常工作流程。

## 主要功能

### 1. 项目管理
- 支持管理多个游戏项目
- 项目间快速切换（左右箭头导航）
- 项目配置持久化存储

### 2. 编译管理
- 集成VS2008编译功能
- 支持多种编译配置（Debug/Release等）
- 实时编译状态监控和输出显示
- 编译成功/失败通知

### 3. 进程管理
- 一键启动/停止前后端进程
- 实时进程状态监控（PID、内存使用、运行时间）
- 支持自定义启动参数
- 支持自定义关闭脚本

### 4. 文件管理
- 导表文件一键复制到多个目标路径
- 可执行文件自动打包压缩
- 快速打开项目和输出文件夹

### 5. VS2008调试集成
- 检测运行中的VS2008实例
- 支持将进程附加到VS调试器（开发中）

## 技术架构

### 开发环境
- .NET 6.0 + WPF
- C# 语言
- Visual Studio 2022

### 主要依赖
- **Newtonsoft.Json**: JSON配置文件序列化
- **NLog**: 日志记录
- **System.Management**: 系统进程管理

### 项目结构
```
OneClickWorkTool/
├── Models/                 # 数据模型
│   ├── ProjectConfig.cs    # 项目配置模型
│   ├── ProcessInfo.cs      # 进程信息模型
│   └── BuildInfo.cs        # 编译信息模型
├── Services/               # 业务服务
│   ├── ConfigurationService.cs  # 配置管理服务
│   ├── BuildService.cs     # 编译服务
│   └── ProcessService.cs   # 进程管理服务
├── Views/                  # 界面视图
│   ├── ProjectConfigWindow.xaml  # 项目配置窗口
│   └── ProjectConfigWindow.xaml.cs
├── MainWindow.xaml         # 主窗口
├── MainWindow.xaml.cs
├── App.xaml               # 应用程序入口
└── App.xaml.cs
```

## 使用说明

### 首次使用
1. 启动应用程序
2. 点击"添加项目"按钮
3. 配置项目基本信息：
   - 项目名称
   - 项目根路径
   - VS2008解决方案文件路径
4. 配置进程信息：
   - 前端/后端可执行文件路径
   - 启动参数（可选）
5. 配置文件管理：
   - 导表源路径
   - 导表目标路径（可多个）
   - 打包输出路径

### 日常使用
1. **项目切换**: 使用顶部的左右箭头或下拉框切换项目
2. **编译项目**: 选择编译配置后点击"编译"按钮
3. **启动进程**: 点击"启动前端"/"启动后端"或"启动全部"
4. **监控状态**: 实时查看编译和进程状态
5. **查看日志**: 底部日志区域显示所有操作记录

### 配置文件位置
- 用户文档目录/OneClickWorkTool/projects.json
- 用户文档目录/OneClickWorkTool/app.config.json
- 日志文件：用户文档目录/OneClickWorkTool/Logs/

## 开发计划

### 已完成功能 ✅
- [x] 项目管理基础框架
- [x] 配置管理系统
- [x] 主界面设计
- [x] 编译服务集成
- [x] 进程管理服务
- [x] 项目配置界面
- [x] 日志系统

### 待开发功能 🚧
- [ ] VS2008进程附加功能
- [ ] 导表文件复制功能
- [ ] 文件打包压缩功能
- [ ] 编译配置自动检测
- [ ] 进程CPU使用率监控
- [ ] 界面主题和美化
- [ ] 快捷键支持
- [ ] 自动保存功能

### 未来计划 📋
- [ ] 支持更多VS版本
- [ ] 插件系统
- [ ] 远程部署功能
- [ ] 团队协作功能

## 编译和运行

### 前提条件
- .NET 6.0 SDK
- Visual Studio 2022 或 VS Code

### 编译步骤
```bash
# 克隆项目
git clone <repository-url>
cd OneClickWorkTool

# 还原依赖
dotnet restore

# 编译项目
dotnet build

# 运行项目
dotnet run
```

### 发布
```bash
# 发布为单文件可执行程序
dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true
```

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交 Issue
- 发送邮件到 [<EMAIL>]

---

**注意**: 本工具专为Windows平台和Visual Studio 2008设计，其他平台和IDE可能需要额外配置。
