# 一键工作工具 - PowerShell 启动脚本
# 适用于 VS Code 环境

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "一键工作工具 - 启动脚本" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 检查 .NET 版本
Write-Host "检查 .NET 版本..." -ForegroundColor Yellow
try {
    $dotnetVersion = dotnet --version
    Write-Host "✅ .NET 版本: $dotnetVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ 错误: 未找到 .NET SDK" -ForegroundColor Red
    Write-Host "请确保已安装 .NET 9.0 SDK" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

# 检查项目文件
$projectPath = "OneClickWorkTool/OneClickWorkTool.csproj"
if (-not (Test-Path $projectPath)) {
    Write-Host "❌ 错误: 找不到项目文件 $projectPath" -ForegroundColor Red
    Read-Host "按任意键退出"
    exit 1
}

Write-Host "✅ 项目文件存在: $projectPath" -ForegroundColor Green
Write-Host ""

# 还原依赖包
Write-Host "还原依赖包..." -ForegroundColor Yellow
Set-Location "OneClickWorkTool"
try {
    dotnet restore
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ 依赖包还原成功" -ForegroundColor Green
    } else {
        throw "还原失败"
    }
} catch {
    Write-Host "❌ 依赖包还原失败" -ForegroundColor Red
    Set-Location ".."
    Read-Host "按任意键退出"
    exit 1
}

Write-Host ""

# 编译项目
Write-Host "编译项目..." -ForegroundColor Yellow
try {
    dotnet build -c Release
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ 编译成功" -ForegroundColor Green
    } else {
        throw "编译失败"
    }
} catch {
    Write-Host "❌ 编译失败" -ForegroundColor Red
    Set-Location ".."
    Read-Host "按任意键退出"
    exit 1
}

Write-Host ""

# 运行项目
Write-Host "启动应用程序..." -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

try {
    dotnet run
} catch {
    Write-Host "❌ 运行失败" -ForegroundColor Red
} finally {
    Set-Location ".."
    Write-Host ""
    Write-Host "应用程序已退出" -ForegroundColor Yellow
    Read-Host "按任意键关闭"
}
