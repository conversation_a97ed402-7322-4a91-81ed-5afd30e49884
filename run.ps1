# OneClickWorkTool - PowerShell Startup Script
# For VS Code Environment

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "OneClickWorkTool - Build & Run Script" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check .NET Version
Write-Host "Checking .NET Version..." -ForegroundColor Yellow
try {
    $dotnetVersion = dotnet --version
    Write-Host "Success - .NET Version: $dotnetVersion" -ForegroundColor Green
} catch {
    Write-Host "Error: .NET SDK not found" -ForegroundColor Red
    Write-Host "Please ensure .NET 9.0 SDK is installed" -ForegroundColor Red
    Read-Host "Press any key to exit"
    exit 1
}

Write-Host ""
Write-Host "Please select an option:" -ForegroundColor Yellow
Write-Host "1. Build and Run (Debug)" -ForegroundColor White
Write-Host "2. Build and Run (Release)" -ForegroundColor White
Write-Host "3. Publish Release (Single File)" -ForegroundColor White
Write-Host "4. Clean and Rebuild" -ForegroundColor White
Write-Host "5. Exit" -ForegroundColor White
Write-Host ""

$choice = Read-Host "Enter your choice (1-5)"

switch ($choice) {
    "1" { $buildConfig = "Debug"; $action = "run" }
    "2" { $buildConfig = "Release"; $action = "run" }
    "3" { $buildConfig = "Release"; $action = "publish" }
    "4" { $buildConfig = "Release"; $action = "clean" }
    "5" { exit 0 }
    default {
        Write-Host "Invalid choice. Defaulting to Build and Run (Debug)" -ForegroundColor Yellow
        $buildConfig = "Debug"; $action = "run"
    }
}

# Check Project File
$projectPath = "OneClickWorkTool/OneClickWorkTool.csproj"
if (-not (Test-Path $projectPath)) {
    Write-Host "Error: Project file not found $projectPath" -ForegroundColor Red
    Read-Host "Press any key to exit"
    exit 1
}

Write-Host "Success - Project file exists: $projectPath" -ForegroundColor Green
Write-Host ""

# Navigate to project directory
Set-Location "OneClickWorkTool"

# Execute based on selected action
switch ($action) {
    "clean" {
        Write-Host "Cleaning project..." -ForegroundColor Yellow
        dotnet clean
        if ($LASTEXITCODE -eq 0) {
            Write-Host "Success - Project cleaned" -ForegroundColor Green
        }
        Write-Host ""
    }
}

# Restore Dependencies
Write-Host "Restoring dependencies..." -ForegroundColor Yellow
try {
    dotnet restore --verbosity normal
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Success - Dependencies restored" -ForegroundColor Green
    } else {
        throw "Restore failed"
    }
} catch {
    Write-Host "Error: Failed to restore dependencies" -ForegroundColor Red
    Set-Location ".."
    Read-Host "Press any key to exit"
    exit 1
}

Write-Host ""

# Build or Publish based on action
if ($action -eq "publish") {
    Write-Host "Publishing project ($buildConfig)..." -ForegroundColor Yellow
    Write-Host "Creating single-file executable..." -ForegroundColor Cyan

    $outputDir = "../bin"
    if (-not (Test-Path $outputDir)) {
        New-Item -ItemType Directory -Path $outputDir | Out-Null
    }

    try {
        dotnet publish -c $buildConfig -r win-x64 --self-contained true -p:PublishSingleFile=true -o $outputDir --verbosity normal
        if ($LASTEXITCODE -eq 0) {
            Write-Host "Success - Project published" -ForegroundColor Green
            Write-Host "Output location: $outputDir" -ForegroundColor Cyan
            Write-Host "Executable: $outputDir/OneClickWorkTool.exe" -ForegroundColor Cyan
        } else {
            throw "Publish failed"
        }
    } catch {
        Write-Host "Error: Publish failed" -ForegroundColor Red
        Set-Location ".."
        Read-Host "Press any key to exit"
        exit 1
    }
} else {
    Write-Host "Building project ($buildConfig)..." -ForegroundColor Yellow
    try {
        dotnet build -c $buildConfig --verbosity normal
        if ($LASTEXITCODE -eq 0) {
            Write-Host "Success - Build completed" -ForegroundColor Green
        } else {
            throw "Build failed"
        }
    } catch {
        Write-Host "Error: Build failed" -ForegroundColor Red
        Set-Location ".."
        Read-Host "Press any key to exit"
        exit 1
    }
}

Write-Host ""

# Run project if action is "run"
if ($action -eq "run") {
    Write-Host "Starting application..." -ForegroundColor Yellow
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host ""

    try {
        dotnet run -c $buildConfig
    } catch {
        Write-Host "Error: Failed to run application" -ForegroundColor Red
    }
}

# Cleanup and exit
Set-Location ".."
Write-Host ""
Write-Host "Operation completed" -ForegroundColor Yellow
Read-Host "Press any key to close"
