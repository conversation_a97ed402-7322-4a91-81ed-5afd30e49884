# OneClickWorkTool - PowerShell Startup Script
# For VS Code Environment

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "OneClickWorkTool - Startup Script" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check .NET Version
Write-Host "Checking .NET Version..." -ForegroundColor Yellow
try {
    $dotnetVersion = dotnet --version
    Write-Host "Success - .NET Version: $dotnetVersion" -ForegroundColor Green
} catch {
    Write-Host "Error: .NET SDK not found" -ForegroundColor Red
    Write-Host "Please ensure .NET 9.0 SDK is installed" -ForegroundColor Red
    Read-Host "Press any key to exit"
    exit 1
}

# Check Project File
$projectPath = "OneClickWorkTool/OneClickWorkTool.csproj"
if (-not (Test-Path $projectPath)) {
    Write-Host "Error: Project file not found $projectPath" -ForegroundColor Red
    Read-Host "Press any key to exit"
    exit 1
}

Write-Host "Success - Project file exists: $projectPath" -ForegroundColor Green
Write-Host ""

# Restore Dependencies
Write-Host "Restoring dependencies..." -ForegroundColor Yellow
Set-Location "OneClickWorkTool"
try {
    dotnet restore
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Success - Dependencies restored" -ForegroundColor Green
    } else {
        throw "Restore failed"
    }
} catch {
    Write-Host "Error: Failed to restore dependencies" -ForegroundColor Red
    Set-Location ".."
    Read-Host "Press any key to exit"
    exit 1
}

Write-Host ""

# Build Project
Write-Host "Building project..." -ForegroundColor Yellow
try {
    dotnet build -c Release
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Success - Build completed" -ForegroundColor Green
    } else {
        throw "Build failed"
    }
} catch {
    Write-Host "Error: Build failed" -ForegroundColor Red
    Set-Location ".."
    Read-Host "Press any key to exit"
    exit 1
}

Write-Host ""

# Run Project
Write-Host "Starting application..." -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

try {
    dotnet run
} catch {
    Write-Host "Error: Failed to run application" -ForegroundColor Red
} finally {
    Set-Location ".."
    Write-Host ""
    Write-Host "Application exited" -ForegroundColor Yellow
    Read-Host "Press any key to close"
}
