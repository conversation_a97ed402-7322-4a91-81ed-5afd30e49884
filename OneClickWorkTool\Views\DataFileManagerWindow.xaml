<Window x:Class="OneClickWorkTool.Views.DataFileManagerWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="数据文件管理 (SData)" Height="700" Width="1000"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize">

    <Window.Resources>
        <Style x:Key="SectionHeader" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Margin" Value="0,10,0,5"/>
            <Setter Property="Foreground" Value="#333333"/>
        </Style>
    </Window.Resources>

    <Grid Margin="15">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 配置区域 -->
        <Border Grid.Row="0" BorderBrush="#E0E0E0" BorderThickness="1" Padding="10" Margin="0,0,0,10">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <TextBlock Grid.Row="0" Text="配置设置" Style="{StaticResource SectionHeader}"/>

                <!-- 源路径配置 -->
                <Grid Grid.Row="1" Margin="0,5">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <Label Grid.Column="0" Content="源路径:" VerticalAlignment="Center" Width="80"/>
                    <TextBox Grid.Column="1" Name="SourcePathTextBox" Margin="5,0" Padding="5"/>
                    <Button Grid.Column="2" Name="BrowseSourceButton" Content="浏览..." Padding="10,5"
                            Margin="5,0" Click="BrowseSourceButton_Click"/>
                    <Button Grid.Column="3" Name="ScanFilesButton" Content="扫描文件" Padding="10,5"
                            Margin="5,0" Click="ScanFilesButton_Click"/>
                </Grid>

                <!-- 目标路径配置 -->
                <Grid Grid.Row="2" Margin="0,5">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <Label Grid.Column="0" Content="目标路径:" VerticalAlignment="Top" Width="80" Margin="0,5"/>
                    <ListBox Grid.Column="1" Name="TargetPathsListBox" Height="80" Margin="5,0"/>
                    <StackPanel Grid.Column="2" Margin="5,0">
                        <Button Name="AddTargetPathButton" Content="添加" Padding="10,5" Margin="0,2"
                                Click="AddTargetPathButton_Click"/>
                        <Button Name="RemoveTargetPathButton" Content="删除" Padding="10,5" Margin="0,2"
                                Click="RemoveTargetPathButton_Click"/>
                    </StackPanel>
                    <StackPanel Grid.Column="3" Margin="5,0">
                        <Button Name="SelectAllFilesButton" Content="全选文件" Padding="10,5" Margin="0,2"
                                Click="SelectAllFilesButton_Click"/>
                        <Button Name="UnselectAllFilesButton" Content="全不选" Padding="10,5" Margin="0,2"
                                Click="UnselectAllFilesButton_Click"/>
                    </StackPanel>
                </Grid>
            </Grid>
        </Border>

        <!-- 状态信息 -->
        <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,0,0,10">
            <TextBlock Name="FileCountText" Text="检测到 0 个 SData 文件" FontWeight="SemiBold"/>
            <TextBlock Name="SelectedCountText" Text="，已选择 0 个" Margin="5,0,0,0"/>
            <TextBlock Name="LastScanTimeText" Text="" Margin="20,0,0,0" Foreground="Gray"/>
        </StackPanel>

        <!-- 文件列表 -->
        <DataGrid Grid.Row="2" Name="SDataFilesDataGrid"
                  AutoGenerateColumns="False" CanUserAddRows="False"
                  GridLinesVisibility="Horizontal" HeadersVisibility="Column"
                  SelectionMode="Extended" CanUserSortColumns="True">
            <DataGrid.Columns>
                <DataGridCheckBoxColumn Header="选择" Binding="{Binding IsSelected}" Width="50"/>
                <DataGridTextColumn Header="文件名" Binding="{Binding FileName}" Width="200" IsReadOnly="True"/>
                <DataGridTextColumn Header="相对路径" Binding="{Binding RelativePath}" Width="250" IsReadOnly="True"/>
                <DataGridTextColumn Header="文件大小" Binding="{Binding FileSizeText}" Width="80" IsReadOnly="True"/>
                <DataGridTextColumn Header="修改时间" Binding="{Binding LastModifiedText}" Width="140" IsReadOnly="True"/>
                <DataGridTemplateColumn Header="目标路径" Width="*">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="{Binding TargetPathsText}" VerticalAlignment="Center"
                                           TextTrimming="CharacterEllipsis"/>
                                <Button Content="选择..." Margin="5,0,0,0" Padding="8,2"
                                        Click="SelectTargetPathsButton_Click" Tag="{Binding}"/>
                            </StackPanel>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
            </DataGrid.Columns>
        </DataGrid>

        <!-- 操作按钮 -->
        <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,15,0,0">
            <Button Name="CopySelectedButton" Content="复制选中文件" Padding="15,8"
                    Click="CopySelectedButton_Click"/>
            <Button Name="RefreshButton" Content="刷新" Padding="15,8" Margin="10,0,0,0"
                    Click="RefreshButton_Click"/>
            <Button Name="CloseButton" Content="关闭" Padding="15,8" Margin="10,0,0,0"
                    Click="CloseButton_Click" IsCancel="True"/>
        </StackPanel>
    </Grid>
</Window>
