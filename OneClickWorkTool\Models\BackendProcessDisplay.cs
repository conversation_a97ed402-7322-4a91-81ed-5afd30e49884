using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Media;

namespace OneClickWorkTool.Models
{
    /// <summary>
    /// 后端进程显示模型（用于UI绑定）
    /// </summary>
    public class BackendProcessDisplay : INotifyPropertyChanged
    {
        private string _name = string.Empty;
        private ProcessStatus _status = ProcessStatus.Stopped;
        private int _processId;
        private long _memoryUsage;
        private DateTime _startTime;

        /// <summary>
        /// 进程名称
        /// </summary>
        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        /// <summary>
        /// 进程状态
        /// </summary>
        public ProcessStatus Status
        {
            get => _status;
            set => SetProperty(ref _status, value);
        }

        /// <summary>
        /// 进程ID
        /// </summary>
        public int ProcessId
        {
            get => _processId;
            set => SetProperty(ref _processId, value);
        }

        /// <summary>
        /// 内存使用量
        /// </summary>
        public long MemoryUsage
        {
            get => _memoryUsage;
            set => SetProperty(ref _memoryUsage, value);
        }

        /// <summary>
        /// 启动时间
        /// </summary>
        public DateTime StartTime
        {
            get => _startTime;
            set => SetProperty(ref _startTime, value);
        }

        /// <summary>
        /// 后端进程配置ID
        /// </summary>
        public Guid ConfigId { get; set; }

        /// <summary>
        /// 状态显示文本
        /// </summary>
        public string StatusText
        {
            get
            {
                return Status switch
                {
                    ProcessStatus.Running => "运行中",
                    ProcessStatus.Stopped => "已停止",
                    ProcessStatus.Starting => "启动中",
                    ProcessStatus.Stopping => "停止中",
                    ProcessStatus.Error => "错误",
                    _ => "未知"
                };
            }
        }

        /// <summary>
        /// 状态颜色
        /// </summary>
        public Brush StatusColor
        {
            get
            {
                return Status switch
                {
                    ProcessStatus.Running => Brushes.Green,
                    ProcessStatus.Starting => Brushes.Orange,
                    ProcessStatus.Stopping => Brushes.Orange,
                    ProcessStatus.Stopped => Brushes.Gray,
                    ProcessStatus.Error => Brushes.Red,
                    _ => Brushes.Gray
                };
            }
        }

        /// <summary>
        /// PID显示文本
        /// </summary>
        public string PidText
        {
            get => ProcessId > 0 ? ProcessId.ToString() : "-";
        }

        /// <summary>
        /// 内存使用量显示文本
        /// </summary>
        public string MemoryText
        {
            get
            {
                if (MemoryUsage <= 0) return "-";

                if (MemoryUsage < 1024 * 1024)
                    return $"{MemoryUsage / 1024:F0}K";
                else if (MemoryUsage < 1024 * 1024 * 1024)
                    return $"{MemoryUsage / (1024 * 1024):F0}M";
                else
                    return $"{MemoryUsage / (1024 * 1024 * 1024):F1}G";
            }
        }

        /// <summary>
        /// 运行时长显示文本
        /// </summary>
        public string RuntimeText
        {
            get
            {
                if (Status != ProcessStatus.Running || StartTime == default)
                    return "-";

                var time = DateTime.Now - StartTime;
                if (time.TotalDays >= 1)
                    return $"{(int)time.TotalDays}d {time.Hours:D2}h";
                else if (time.TotalHours >= 1)
                    return $"{time.Hours:D2}:{time.Minutes:D2}h";
                else
                    return $"{time.Minutes:D2}:{time.Seconds:D2}";
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);

            // 当状态或其他相关属性改变时，通知相关的显示属性
            if (propertyName == nameof(Status))
            {
                OnPropertyChanged(nameof(StatusText));
                OnPropertyChanged(nameof(StatusColor));
                OnPropertyChanged(nameof(RuntimeText));
            }
            else if (propertyName == nameof(ProcessId))
            {
                OnPropertyChanged(nameof(PidText));
            }
            else if (propertyName == nameof(MemoryUsage))
            {
                OnPropertyChanged(nameof(MemoryText));
            }
            else if (propertyName == nameof(StartTime))
            {
                OnPropertyChanged(nameof(RuntimeText));
            }

            return true;
        }

        /// <summary>
        /// 从ProcessInfo更新显示信息
        /// </summary>
        public void UpdateFromProcessInfo(ProcessInfo processInfo)
        {
            Status = processInfo.Status;
            ProcessId = processInfo.ProcessId;
            MemoryUsage = processInfo.MemoryUsage;
            StartTime = processInfo.StartTime;
        }

        /// <summary>
        /// 从BackendProcessConfig创建显示对象
        /// </summary>
        public static BackendProcessDisplay FromConfig(BackendProcessConfig config)
        {
            return new BackendProcessDisplay
            {
                Name = config.Name,
                ConfigId = config.Id,
                Status = ProcessStatus.Stopped
            };
        }
    }
}
