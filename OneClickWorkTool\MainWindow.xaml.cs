using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using OneClickWorkTool.Models;
using OneClickWorkTool.Services;
using OneClickWorkTool.Views;
using NLog;

namespace OneClickWorkTool
{
    /// <summary>
    /// MainWindow.xaml 的交互逻辑
    /// </summary>
    public partial class MainWindow : Window
    {
        private static readonly Logger Logger = LogManager.GetCurrentClassLogger();

        private readonly ConfigurationService _configService;
        private readonly BuildService _buildService;
        private readonly ProcessService _processService;

        private ProjectConfig? _currentProject;
        private BuildInfo _buildInfo = new BuildInfo();
        private List<ProjectConfig> _projects = new List<ProjectConfig>();

        public MainWindow()
        {
            InitializeComponent();

            _configService = new ConfigurationService();
            _buildService = new BuildService(_configService.GetAppConfig().VS2008Path);
            _processService = new ProcessService();

            InitializeServices();
            LoadProjects();
            InitializeUI();

            Logger.Info("主窗口初始化完成");
        }

        private void InitializeServices()
        {
            // 订阅编译服务事件
            _buildService.BuildStatusChanged += OnBuildStatusChanged;
            _buildService.BuildOutputReceived += OnBuildOutputReceived;

            // 订阅进程服务事件
            _processService.ProcessStatusChanged += OnProcessStatusChanged;
        }

        private void LoadProjects()
        {
            try
            {
                _configService.LoadConfigurations();
                _projects = _configService.GetAllProjects();

                ProjectComboBox.ItemsSource = _projects;

                if (_projects.Any())
                {
                    _currentProject = _configService.GetActiveProject();
                    if (_currentProject != null)
                    {
                        ProjectComboBox.SelectedItem = _currentProject;
                    }
                    else
                    {
                        ProjectComboBox.SelectedIndex = 0;
                    }
                }

                UpdateProjectUI();
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "加载项目配置时发生错误");
                AppendLog($"错误: 加载项目配置失败 - {ex.Message}");
            }
        }

        private void InitializeUI()
        {
            // 初始化编译配置
            BuildConfigComboBox.ItemsSource = new[] { "Debug", "Release" };
            BuildConfigComboBox.SelectedItem = "Release";

            BuildPlatformComboBox.ItemsSource = new[] { "Win32", "x64" };
            BuildPlatformComboBox.SelectedItem = "Win32";

            // 初始化状态指示器
            UpdateBuildStatusUI();
            UpdateProcessStatusUI();
        }

        private async void UpdateProjectUI()
        {
            if (_currentProject == null)
            {
                Title = "一键工作工具";
                return;
            }

            Title = $"一键工作工具 - {_currentProject.DisplayName}";

            // 自动检测编译配置
            await LoadBuildConfigurations();

            AppendLog($"切换到项目: {_currentProject.DisplayName}");
        }

        private async Task LoadBuildConfigurations()
        {
            if (_currentProject == null) return;

            try
            {
                // 如果有解决方案文件，尝试自动检测配置
                if (!string.IsNullOrWhiteSpace(_currentProject.SolutionPath) &&
                    System.IO.File.Exists(_currentProject.SolutionPath))
                {
                    AppendLog("正在检测解决方案编译配置...");

                    var (configs, platforms) = await _buildService.DetectBuildConfigurationsAsync(_currentProject.SolutionPath);

                    _currentProject.AvailableBuildConfigurations.Clear();
                    _currentProject.AvailableBuildConfigurations.AddRange(configs);

                    _currentProject.AvailableBuildPlatforms.Clear();
                    _currentProject.AvailableBuildPlatforms.AddRange(platforms);

                    AppendLog($"检测到编译配置: {string.Join(", ", configs)}");
                    AppendLog($"检测到编译平台: {string.Join(", ", platforms)}");
                }

                // 更新UI
                var allConfigs = _currentProject.GetAllBuildConfigurations();
                var allPlatforms = _currentProject.GetAllBuildPlatforms();

                BuildConfigComboBox.ItemsSource = allConfigs;
                BuildPlatformComboBox.ItemsSource = allPlatforms;

                // 设置默认选择
                if (allConfigs.Contains(_currentProject.BuildConfiguration))
                {
                    BuildConfigComboBox.SelectedItem = _currentProject.BuildConfiguration;
                }
                else if (allConfigs.Any())
                {
                    BuildConfigComboBox.SelectedIndex = 0;
                }

                if (allPlatforms.Contains(_currentProject.BuildPlatform))
                {
                    BuildPlatformComboBox.SelectedItem = _currentProject.BuildPlatform;
                }
                else if (allPlatforms.Any())
                {
                    BuildPlatformComboBox.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "加载编译配置时发生错误");
                AppendLog($"加载编译配置失败: {ex.Message}");
            }
        }

        private void UpdateBuildStatusUI()
        {
            Dispatcher.Invoke(() =>
            {
                BuildStatusText.Text = _buildInfo.StatusText;

                BuildStatusIndicator.Fill = _buildInfo.Status switch
                {
                    BuildStatus.Ready => Brushes.Gray,
                    BuildStatus.Building => Brushes.Orange,
                    BuildStatus.Success => Brushes.Green,
                    BuildStatus.Failed => Brushes.Red,
                    BuildStatus.Cancelled => Brushes.Gray,
                    _ => Brushes.Gray
                };

                BuildButton.IsEnabled = _buildInfo.CanBuild;
                CancelBuildButton.IsEnabled = _buildInfo.IsBuilding;
            });
        }

        private void UpdateProcessStatusUI()
        {
            Dispatcher.Invoke(() =>
            {
                var frontendProcess = _processService.GetProcessInfo(ProcessType.Frontend);
                var backendProcess = _processService.GetProcessInfo(ProcessType.Backend);

                UpdateProcessUI(frontendProcess, FrontendStatusIndicator, FrontendStatusText,
                    FrontendProcessIdText, FrontendMemoryText, FrontendRuntimeText);

                UpdateProcessUI(backendProcess, BackendStatusIndicator, BackendStatusText,
                    BackendProcessIdText, BackendMemoryText, BackendRuntimeText);
            });
        }

        private void UpdateProcessUI(ProcessInfo? processInfo, System.Windows.Shapes.Ellipse indicator,
            TextBlock statusText, TextBlock pidText, TextBlock memoryText, TextBlock runtimeText)
        {
            if (processInfo == null)
            {
                indicator.Fill = Brushes.Gray;
                statusText.Text = "未配置";
                pidText.Text = "PID: -";
                memoryText.Text = "内存: -";
                runtimeText.Text = "运行时间: -";
                return;
            }

            indicator.Fill = processInfo.Status switch
            {
                ProcessStatus.Running => Brushes.Green,
                ProcessStatus.Starting => Brushes.Orange,
                ProcessStatus.Stopping => Brushes.Orange,
                ProcessStatus.Stopped => Brushes.Gray,
                ProcessStatus.Error => Brushes.Red,
                _ => Brushes.Gray
            };

            statusText.Text = processInfo.StatusText;
            pidText.Text = $"PID: {(processInfo.ProcessId > 0 ? processInfo.ProcessId.ToString() : "-")}";
            memoryText.Text = $"内存: {processInfo.MemoryUsageText}";
            runtimeText.Text = $"运行时间: {processInfo.RunningTimeText}";
        }

        private void AppendLog(string message)
        {
            Dispatcher.Invoke(() =>
            {
                var timestamp = DateTime.Now.ToString("HH:mm:ss");
                LogTextBox.AppendText($"[{timestamp}] {message}\n");
                LogTextBox.ScrollToEnd();
            });
        }

        #region 事件处理

        private void OnBuildStatusChanged(object? sender, BuildInfo buildInfo)
        {
            _buildInfo = buildInfo;
            UpdateBuildStatusUI();
            AppendLog($"编译状态: {buildInfo.ResultSummary}");
        }

        private void OnBuildOutputReceived(object? sender, string output)
        {
            AppendLog($"编译输出: {output}");
        }

        private void OnProcessStatusChanged(object? sender, ProcessInfo processInfo)
        {
            UpdateProcessStatusUI();
            AppendLog($"{processInfo.Type} 进程状态: {processInfo.StatusText}");
        }

        #endregion

        #region UI事件处理

        private void ProjectComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (ProjectComboBox.SelectedItem is ProjectConfig selectedProject)
            {
                _currentProject = selectedProject;
                _configService.SetActiveProject(selectedProject.Id);
                UpdateProjectUI();
            }
        }

        private void PrevProjectButton_Click(object sender, RoutedEventArgs e)
        {
            if (_projects.Count <= 1) return;

            var currentIndex = ProjectComboBox.SelectedIndex;
            var newIndex = currentIndex > 0 ? currentIndex - 1 : _projects.Count - 1;
            ProjectComboBox.SelectedIndex = newIndex;
        }

        private void NextProjectButton_Click(object sender, RoutedEventArgs e)
        {
            if (_projects.Count <= 1) return;

            var currentIndex = ProjectComboBox.SelectedIndex;
            var newIndex = currentIndex < _projects.Count - 1 ? currentIndex + 1 : 0;
            ProjectComboBox.SelectedIndex = newIndex;
        }

        private void AddProjectButton_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new ProjectConfigWindow();
            if (dialog.ShowDialog() == true && dialog.ProjectConfig != null)
            {
                try
                {
                    _configService.AddProject(dialog.ProjectConfig);
                    LoadProjects();
                    ProjectComboBox.SelectedItem = dialog.ProjectConfig;
                    AppendLog($"添加项目成功: {dialog.ProjectConfig.Name}");
                }
                catch (Exception ex)
                {
                    Logger.Error(ex, "添加项目时发生错误");
                    MessageBox.Show($"添加项目失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void SettingsButton_Click(object sender, RoutedEventArgs e)
        {
            if (_currentProject == null)
            {
                MessageBox.Show("请先选择一个项目", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            var dialog = new ProjectConfigWindow(_currentProject);
            if (dialog.ShowDialog() == true && dialog.ProjectConfig != null)
            {
                try
                {
                    _configService.UpdateProject(dialog.ProjectConfig);
                    LoadProjects();
                    AppendLog($"更新项目配置成功: {dialog.ProjectConfig.Name}");
                }
                catch (Exception ex)
                {
                    Logger.Error(ex, "更新项目配置时发生错误");
                    MessageBox.Show($"更新项目配置失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void AdvancedConfigButton_Click(object sender, RoutedEventArgs e)
        {
            if (_currentProject == null)
            {
                MessageBox.Show("请先选择一个项目", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            var dialog = new AdvancedConfigWindow(_currentProject, _buildService);
            if (dialog.ShowDialog() == true)
            {
                try
                {
                    _configService.UpdateProject(_currentProject);
                    await LoadBuildConfigurations(); // 重新加载编译配置
                    AppendLog($"更新高级配置成功: {_currentProject.Name}");
                }
                catch (Exception ex)
                {
                    Logger.Error(ex, "更新高级配置时发生错误");
                    MessageBox.Show($"更新高级配置失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private async void BuildButton_Click(object sender, RoutedEventArgs e)
        {
            if (_currentProject == null)
            {
                MessageBox.Show("请先选择一个项目", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            _buildInfo.Configuration = BuildConfigComboBox.SelectedItem?.ToString() ?? "Release";
            _buildInfo.Platform = BuildPlatformComboBox.SelectedItem?.ToString() ?? "Win32";

            AppendLog($"开始编译项目: {_currentProject.Name}");

            try
            {
                var success = await _buildService.StartBuildAsync(_currentProject, _buildInfo);
                if (success)
                {
                    AppendLog("编译完成");
                }
                else
                {
                    AppendLog("编译失败");
                }
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "编译时发生错误");
                AppendLog($"编译错误: {ex.Message}");
            }
        }

        private void CancelBuildButton_Click(object sender, RoutedEventArgs e)
        {
            _buildService.CancelBuild();
            AppendLog("取消编译");
        }

        private async void StartFrontendButton_Click(object sender, RoutedEventArgs e)
        {
            if (_currentProject == null || string.IsNullOrWhiteSpace(_currentProject.FrontendExePath))
            {
                MessageBox.Show("请先配置前端可执行文件路径", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            AppendLog("启动前端进程...");
            await _processService.StartProcessAsync(ProcessType.Frontend, _currentProject.FrontendExePath, _currentProject.FrontendStartupArgs);
        }

        private async void StartBackendButton_Click(object sender, RoutedEventArgs e)
        {
            if (_currentProject == null || string.IsNullOrWhiteSpace(_currentProject.BackendExePath))
            {
                MessageBox.Show("请先配置后端可执行文件路径", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            AppendLog("启动后端进程...");
            await _processService.StartProcessAsync(ProcessType.Backend, _currentProject.BackendExePath, _currentProject.BackendStartupArgs);
        }

        private async void StartAllButton_Click(object sender, RoutedEventArgs e)
        {
            if (_currentProject == null)
            {
                MessageBox.Show("请先选择一个项目", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            AppendLog("启动所有进程...");

            if (!string.IsNullOrWhiteSpace(_currentProject.FrontendExePath))
            {
                await _processService.StartProcessAsync(ProcessType.Frontend, _currentProject.FrontendExePath, _currentProject.FrontendStartupArgs);
            }

            if (!string.IsNullOrWhiteSpace(_currentProject.BackendExePath))
            {
                await _processService.StartProcessAsync(ProcessType.Backend, _currentProject.BackendExePath, _currentProject.BackendStartupArgs);
            }
        }

        private void StopFrontendButton_Click(object sender, RoutedEventArgs e)
        {
            AppendLog("停止前端进程...");
            _processService.StopProcess(ProcessType.Frontend);
        }

        private void StopBackendButton_Click(object sender, RoutedEventArgs e)
        {
            AppendLog("停止后端进程...");
            _processService.StopProcess(ProcessType.Backend);
        }

        private void StopAllButton_Click(object sender, RoutedEventArgs e)
        {
            StopFrontendButton_Click(sender, e);
            StopBackendButton_Click(sender, e);
        }

        private void AttachToVSButton_Click(object sender, RoutedEventArgs e)
        {
            // TODO: 实现VS附加功能
            AppendLog("VS附加功能开发中...");
        }

        private void CopyDataTablesButton_Click(object sender, RoutedEventArgs e)
        {
            // TODO: 实现导表文件复制功能
            AppendLog("导表文件复制功能开发中...");
        }

        private void PackageFilesButton_Click(object sender, RoutedEventArgs e)
        {
            // TODO: 实现文件打包功能
            AppendLog("文件打包功能开发中...");
        }

        private void OpenProjectFolderButton_Click(object sender, RoutedEventArgs e)
        {
            if (_currentProject != null && !string.IsNullOrWhiteSpace(_currentProject.ProjectPath))
            {
                System.Diagnostics.Process.Start("explorer.exe", _currentProject.ProjectPath);
            }
        }

        private void OpenOutputFolderButton_Click(object sender, RoutedEventArgs e)
        {
            if (_currentProject != null && !string.IsNullOrWhiteSpace(_currentProject.PackageOutputPath))
            {
                System.Diagnostics.Process.Start("explorer.exe", _currentProject.PackageOutputPath);
            }
        }

        private void ClearLogButton_Click(object sender, RoutedEventArgs e)
        {
            LogTextBox.Clear();
        }

        #endregion

        protected override void OnClosed(EventArgs e)
        {
            _buildService?.Dispose();
            _processService?.Dispose();
            base.OnClosed(e);
        }
    }
}
