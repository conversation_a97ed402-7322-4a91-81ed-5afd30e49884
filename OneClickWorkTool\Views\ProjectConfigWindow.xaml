<Window x:Class="OneClickWorkTool.Views.ProjectConfigWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="项目配置" Height="600" Width="800"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize">
    
    <Window.Resources>
        <Style x:Key="SectionHeader" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Margin" Value="0,10,0,5"/>
            <Setter Property="Foreground" Value="#333333"/>
        </Style>
        
        <Style x:Key="FieldLabel" TargetType="Label">
            <Setter Property="Width" Value="120"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="HorizontalAlignment" Value="Right"/>
            <Setter Property="Margin" Value="0,2"/>
        </Style>
        
        <Style x:Key="FieldTextBox" TargetType="TextBox">
            <Setter Property="Margin" Value="5,2"/>
            <Setter Property="Padding" Value="5"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>
        
        <Style x:Key="BrowseButton" TargetType="Button">
            <Setter Property="Content" Value="浏览..."/>
            <Setter Property="Width" Value="60"/>
            <Setter Property="Margin" Value="5,2"/>
            <Setter Property="Padding" Value="5"/>
        </Style>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <ScrollViewer Grid.Row="0" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- 基本信息 -->
                <TextBlock Text="基本信息" Style="{StaticResource SectionHeader}"/>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <Label Grid.Row="0" Grid.Column="0" Content="项目名称:" Style="{StaticResource FieldLabel}"/>
                    <TextBox Grid.Row="0" Grid.Column="1" Name="ProjectNameTextBox" Style="{StaticResource FieldTextBox}"/>

                    <Label Grid.Row="1" Grid.Column="0" Content="项目路径:" Style="{StaticResource FieldLabel}"/>
                    <Grid Grid.Row="1" Grid.Column="1">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBox Grid.Column="0" Name="ProjectPathTextBox" Style="{StaticResource FieldTextBox}"/>
                        <Button Grid.Column="1" Name="BrowseProjectPathButton" Style="{StaticResource BrowseButton}" Click="BrowseProjectPathButton_Click"/>
                    </Grid>
                </Grid>

                <!-- 编译配置 -->
                <TextBlock Text="编译配置" Style="{StaticResource SectionHeader}"/>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <Label Grid.Row="0" Grid.Column="0" Content="解决方案文件:" Style="{StaticResource FieldLabel}"/>
                    <Grid Grid.Row="0" Grid.Column="1">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBox Grid.Column="0" Name="SolutionPathTextBox" Style="{StaticResource FieldTextBox}"/>
                        <Button Grid.Column="1" Name="BrowseSolutionPathButton" Style="{StaticResource BrowseButton}" Click="BrowseSolutionPathButton_Click"/>
                    </Grid>

                    <Label Grid.Row="1" Grid.Column="0" Content="默认编译配置:" Style="{StaticResource FieldLabel}"/>
                    <ComboBox Grid.Row="1" Grid.Column="1" Name="BuildConfigComboBox" Margin="5,2" Padding="5"/>

                    <Label Grid.Row="2" Grid.Column="0" Content="默认编译平台:" Style="{StaticResource FieldLabel}"/>
                    <ComboBox Grid.Row="2" Grid.Column="1" Name="BuildPlatformComboBox" Margin="5,2" Padding="5"/>
                </Grid>

                <!-- 进程配置 -->
                <TextBlock Text="进程配置" Style="{StaticResource SectionHeader}"/>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <Label Grid.Row="0" Grid.Column="0" Content="前端可执行文件:" Style="{StaticResource FieldLabel}"/>
                    <Grid Grid.Row="0" Grid.Column="1">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBox Grid.Column="0" Name="FrontendExePathTextBox" Style="{StaticResource FieldTextBox}"/>
                        <Button Grid.Column="1" Name="BrowseFrontendExeButton" Style="{StaticResource BrowseButton}" Click="BrowseFrontendExeButton_Click"/>
                    </Grid>

                    <Label Grid.Row="1" Grid.Column="0" Content="前端启动参数:" Style="{StaticResource FieldLabel}"/>
                    <TextBox Grid.Row="1" Grid.Column="1" Name="FrontendArgsTextBox" Style="{StaticResource FieldTextBox}"/>

                    <Label Grid.Row="2" Grid.Column="0" Content="后端可执行文件:" Style="{StaticResource FieldLabel}"/>
                    <Grid Grid.Row="2" Grid.Column="1">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBox Grid.Column="0" Name="BackendExePathTextBox" Style="{StaticResource FieldTextBox}"/>
                        <Button Grid.Column="1" Name="BrowseBackendExeButton" Style="{StaticResource BrowseButton}" Click="BrowseBackendExeButton_Click"/>
                    </Grid>

                    <Label Grid.Row="3" Grid.Column="0" Content="后端启动参数:" Style="{StaticResource FieldLabel}"/>
                    <TextBox Grid.Row="3" Grid.Column="1" Name="BackendArgsTextBox" Style="{StaticResource FieldTextBox}"/>
                </Grid>

                <!-- 文件管理配置 -->
                <TextBlock Text="文件管理配置" Style="{StaticResource SectionHeader}"/>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <Label Grid.Row="0" Grid.Column="0" Content="导表源路径:" Style="{StaticResource FieldLabel}"/>
                    <Grid Grid.Row="0" Grid.Column="1">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBox Grid.Column="0" Name="DataTableSourcePathTextBox" Style="{StaticResource FieldTextBox}"/>
                        <Button Grid.Column="1" Name="BrowseDataTableSourceButton" Style="{StaticResource BrowseButton}" Click="BrowseDataTableSourceButton_Click"/>
                    </Grid>

                    <Label Grid.Row="1" Grid.Column="0" Content="导表目标路径:" Style="{StaticResource FieldLabel}" VerticalAlignment="Top" Margin="0,10,0,0"/>
                    <StackPanel Grid.Row="1" Grid.Column="1" Margin="5,2">
                        <ListBox Name="DataTableTargetPathsListBox" Height="100" Margin="0,5"/>
                        <StackPanel Orientation="Horizontal">
                            <Button Name="AddTargetPathButton" Content="添加路径" Margin="0,5,5,5" Padding="10,5" Click="AddTargetPathButton_Click"/>
                            <Button Name="RemoveTargetPathButton" Content="删除路径" Margin="5" Padding="10,5" Click="RemoveTargetPathButton_Click"/>
                        </StackPanel>
                    </StackPanel>

                    <Label Grid.Row="2" Grid.Column="0" Content="打包输出路径:" Style="{StaticResource FieldLabel}"/>
                    <Grid Grid.Row="2" Grid.Column="1">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBox Grid.Column="0" Name="PackageOutputPathTextBox" Style="{StaticResource FieldTextBox}"/>
                        <Button Grid.Column="1" Name="BrowsePackageOutputButton" Style="{StaticResource BrowseButton}" Click="BrowsePackageOutputButton_Click"/>
                    </Grid>
                </Grid>
            </StackPanel>
        </ScrollViewer>

        <!-- 按钮区域 -->
        <StackPanel Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,20,0,0">
            <Button Name="OkButton" Content="确定" Width="80" Height="30" Margin="5" Click="OkButton_Click" IsDefault="True"/>
            <Button Name="CancelButton" Content="取消" Width="80" Height="30" Margin="5" Click="CancelButton_Click" IsCancel="True"/>
        </StackPanel>
    </Grid>
</Window>
