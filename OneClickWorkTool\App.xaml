<Application x:Class="OneClickWorkTool.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             StartupUri="MainWindow.xaml">
    <Application.Resources>
        <!-- 全局样式和资源 -->
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- 可以在这里添加主题资源字典 -->
            </ResourceDictionary.MergedDictionaries>
            
            <!-- 全局字体设置 -->
            <Style TargetType="{x:Type Window}">
                <Setter Property="FontFamily" Value="Microsoft YaHei UI, Segoe UI, Arial"/>
                <Setter Property="FontSize" Value="12"/>
            </Style>
            
            <Style TargetType="{x:Type UserControl}">
                <Setter Property="FontFamily" Value="Microsoft YaHei UI, Segoe UI, Arial"/>
                <Setter Property="FontSize" Value="12"/>
            </Style>
        </ResourceDictionary>
    </Application.Resources>
</Application>
