﻿#pragma checksum "..\..\..\MainWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "8F0868A0F988817D15F1EFC70D61DEB2EE020375"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace OneClickWorkTool {
    
    
    /// <summary>
    /// MainWindow
    /// </summary>
    public partial class MainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 67 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PrevProjectButton;
        
        #line default
        #line hidden
        
        
        #line 68 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ProjectComboBox;
        
        #line default
        #line hidden
        
        
        #line 71 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button NextProjectButton;
        
        #line default
        #line hidden
        
        
        #line 72 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddProjectButton;
        
        #line default
        #line hidden
        
        
        #line 73 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SettingsButton;
        
        #line default
        #line hidden
        
        
        #line 74 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AdvancedConfigButton;
        
        #line default
        #line hidden
        
        
        #line 90 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox BuildConfigComboBox;
        
        #line default
        #line hidden
        
        
        #line 91 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox BuildPlatformComboBox;
        
        #line default
        #line hidden
        
        
        #line 94 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BuildButton;
        
        #line default
        #line hidden
        
        
        #line 95 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelBuildButton;
        
        #line default
        #line hidden
        
        
        #line 99 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Ellipse BuildStatusIndicator;
        
        #line default
        #line hidden
        
        
        #line 100 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock BuildStatusText;
        
        #line default
        #line hidden
        
        
        #line 115 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StartFrontendButton;
        
        #line default
        #line hidden
        
        
        #line 116 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StartBackendButton;
        
        #line default
        #line hidden
        
        
        #line 117 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StartAllButton;
        
        #line default
        #line hidden
        
        
        #line 118 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StopFrontendButton;
        
        #line default
        #line hidden
        
        
        #line 119 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StopBackendButton;
        
        #line default
        #line hidden
        
        
        #line 120 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StopAllButton;
        
        #line default
        #line hidden
        
        
        #line 121 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AttachToVSButton;
        
        #line default
        #line hidden
        
        
        #line 136 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Ellipse FrontendStatusIndicator;
        
        #line default
        #line hidden
        
        
        #line 137 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FrontendStatusText;
        
        #line default
        #line hidden
        
        
        #line 139 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FrontendProcessIdText;
        
        #line default
        #line hidden
        
        
        #line 140 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FrontendMemoryText;
        
        #line default
        #line hidden
        
        
        #line 141 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FrontendRuntimeText;
        
        #line default
        #line hidden
        
        
        #line 155 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock BackendProcessCountText;
        
        #line default
        #line hidden
        
        
        #line 159 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ItemsControl BackendProcessList;
        
        #line default
        #line hidden
        
        
        #line 192 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CopyDataTablesButton;
        
        #line default
        #line hidden
        
        
        #line 193 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PackageFilesButton;
        
        #line default
        #line hidden
        
        
        #line 194 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenProjectFolderButton;
        
        #line default
        #line hidden
        
        
        #line 195 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenOutputFolderButton;
        
        #line default
        #line hidden
        
        
        #line 209 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearLogButton;
        
        #line default
        #line hidden
        
        
        #line 214 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LogTextBox;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/OneClickWorkTool;V1.0.0.0;component/mainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\MainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.PrevProjectButton = ((System.Windows.Controls.Button)(target));
            
            #line 67 "..\..\..\MainWindow.xaml"
            this.PrevProjectButton.Click += new System.Windows.RoutedEventHandler(this.PrevProjectButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.ProjectComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 69 "..\..\..\MainWindow.xaml"
            this.ProjectComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.ProjectComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 3:
            this.NextProjectButton = ((System.Windows.Controls.Button)(target));
            
            #line 71 "..\..\..\MainWindow.xaml"
            this.NextProjectButton.Click += new System.Windows.RoutedEventHandler(this.NextProjectButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.AddProjectButton = ((System.Windows.Controls.Button)(target));
            
            #line 72 "..\..\..\MainWindow.xaml"
            this.AddProjectButton.Click += new System.Windows.RoutedEventHandler(this.AddProjectButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.SettingsButton = ((System.Windows.Controls.Button)(target));
            
            #line 73 "..\..\..\MainWindow.xaml"
            this.SettingsButton.Click += new System.Windows.RoutedEventHandler(this.SettingsButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.AdvancedConfigButton = ((System.Windows.Controls.Button)(target));
            
            #line 74 "..\..\..\MainWindow.xaml"
            this.AdvancedConfigButton.Click += new System.Windows.RoutedEventHandler(this.AdvancedConfigButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.BuildConfigComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 8:
            this.BuildPlatformComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 9:
            this.BuildButton = ((System.Windows.Controls.Button)(target));
            
            #line 94 "..\..\..\MainWindow.xaml"
            this.BuildButton.Click += new System.Windows.RoutedEventHandler(this.BuildButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.CancelBuildButton = ((System.Windows.Controls.Button)(target));
            
            #line 95 "..\..\..\MainWindow.xaml"
            this.CancelBuildButton.Click += new System.Windows.RoutedEventHandler(this.CancelBuildButton_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.BuildStatusIndicator = ((System.Windows.Shapes.Ellipse)(target));
            return;
            case 12:
            this.BuildStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.StartFrontendButton = ((System.Windows.Controls.Button)(target));
            
            #line 115 "..\..\..\MainWindow.xaml"
            this.StartFrontendButton.Click += new System.Windows.RoutedEventHandler(this.StartFrontendButton_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.StartBackendButton = ((System.Windows.Controls.Button)(target));
            
            #line 116 "..\..\..\MainWindow.xaml"
            this.StartBackendButton.Click += new System.Windows.RoutedEventHandler(this.StartBackendButton_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.StartAllButton = ((System.Windows.Controls.Button)(target));
            
            #line 117 "..\..\..\MainWindow.xaml"
            this.StartAllButton.Click += new System.Windows.RoutedEventHandler(this.StartAllButton_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.StopFrontendButton = ((System.Windows.Controls.Button)(target));
            
            #line 118 "..\..\..\MainWindow.xaml"
            this.StopFrontendButton.Click += new System.Windows.RoutedEventHandler(this.StopFrontendButton_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.StopBackendButton = ((System.Windows.Controls.Button)(target));
            
            #line 119 "..\..\..\MainWindow.xaml"
            this.StopBackendButton.Click += new System.Windows.RoutedEventHandler(this.StopBackendButton_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            this.StopAllButton = ((System.Windows.Controls.Button)(target));
            
            #line 120 "..\..\..\MainWindow.xaml"
            this.StopAllButton.Click += new System.Windows.RoutedEventHandler(this.StopAllButton_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.AttachToVSButton = ((System.Windows.Controls.Button)(target));
            
            #line 121 "..\..\..\MainWindow.xaml"
            this.AttachToVSButton.Click += new System.Windows.RoutedEventHandler(this.AttachToVSButton_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            this.FrontendStatusIndicator = ((System.Windows.Shapes.Ellipse)(target));
            return;
            case 21:
            this.FrontendStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 22:
            this.FrontendProcessIdText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 23:
            this.FrontendMemoryText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 24:
            this.FrontendRuntimeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 25:
            this.BackendProcessCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 26:
            this.BackendProcessList = ((System.Windows.Controls.ItemsControl)(target));
            return;
            case 27:
            this.CopyDataTablesButton = ((System.Windows.Controls.Button)(target));
            
            #line 192 "..\..\..\MainWindow.xaml"
            this.CopyDataTablesButton.Click += new System.Windows.RoutedEventHandler(this.CopyDataTablesButton_Click);
            
            #line default
            #line hidden
            return;
            case 28:
            this.PackageFilesButton = ((System.Windows.Controls.Button)(target));
            
            #line 193 "..\..\..\MainWindow.xaml"
            this.PackageFilesButton.Click += new System.Windows.RoutedEventHandler(this.PackageFilesButton_Click);
            
            #line default
            #line hidden
            return;
            case 29:
            this.OpenProjectFolderButton = ((System.Windows.Controls.Button)(target));
            
            #line 194 "..\..\..\MainWindow.xaml"
            this.OpenProjectFolderButton.Click += new System.Windows.RoutedEventHandler(this.OpenProjectFolderButton_Click);
            
            #line default
            #line hidden
            return;
            case 30:
            this.OpenOutputFolderButton = ((System.Windows.Controls.Button)(target));
            
            #line 195 "..\..\..\MainWindow.xaml"
            this.OpenOutputFolderButton.Click += new System.Windows.RoutedEventHandler(this.OpenOutputFolderButton_Click);
            
            #line default
            #line hidden
            return;
            case 31:
            this.ClearLogButton = ((System.Windows.Controls.Button)(target));
            
            #line 210 "..\..\..\MainWindow.xaml"
            this.ClearLogButton.Click += new System.Windows.RoutedEventHandler(this.ClearLogButton_Click);
            
            #line default
            #line hidden
            return;
            case 32:
            this.LogTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

