这个工具是为了方便工作的。
为是一名C++程序员。
我的工作主要是使用Visual Studio 2008 开发游戏项目前后端。
我需要这个工具可以：
1.打开某个工程（我提供工程所在路径），支持开启多个工程，界面用左右箭头的方式切换不同工程项目。
2.工程应该配套一套使用工具。
    (1)可以点击编译对应版本(直接链接到vs2008的编译按钮)，需要支持选择Release或者debug或者其它版本，应该能读取到可编译的版本。
    (2)支持分别启动前后端进程(我会给进程路径)。关闭前后端进程(我会给关闭的脚本路径)。
    (3)根据导表工具的导出路径(我会给路径)，提供一键复制粘贴到对应项目的路径中(路径应该有多个，支持我设置，每次粘贴的时候我自己点击某个路径实现一键粘贴)
    (4)根据我对项目路径中的对应文件(一般是前后端可执行文件)的路径设置，可以让我一键复制这个文件到对应路径下，然后实现压缩，方便我打包发送给测试。
3.工程应该有一套完整美观便捷的界面，所有路径的配置应该在一个很方便的设置中，可以让我自己浏览选择路径，以及选择对应的文件一键复制压缩。
4.编译成功后应该提示我，编译成功。
5.工具应该支持我用vs2008附加对应进程，这里我不知道如何让工具帮助我链接对应进程，我是不是应该提供对应进程的信息？请帮助我。我有可能打开多个vs2008进程，启动后，我会给你我可能会绑定的对应进程，让我自己选择某个vs2008绑定某些(可能是多个)进程.

