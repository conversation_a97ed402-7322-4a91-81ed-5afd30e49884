using System;
using System.IO;
using System.Windows;
using NLog;
using NLog.Config;
using NLog.Targets;

namespace OneClickWorkTool
{
    /// <summary>
    /// App.xaml 的交互逻辑
    /// </summary>
    public partial class App : Application
    {
        private static readonly Logger Logger = LogManager.GetCurrentClassLogger();

        protected override void OnStartup(StartupEventArgs e)
        {
            // 配置日志
            ConfigureLogging();
            
            // 设置全局异常处理
            SetupExceptionHandling();
            
            Logger.Info("应用程序启动");
            
            base.OnStartup(e);
        }

        protected override void OnExit(ExitEventArgs e)
        {
            Logger.Info("应用程序退出");
            LogManager.Shutdown();
            base.OnExit(e);
        }

        private void ConfigureLogging()
        {
            var config = new LoggingConfiguration();

            // 创建日志目录
            var logDirectory = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments),
                "OneClickWorkTool", "Logs");
            
            if (!Directory.Exists(logDirectory))
            {
                Directory.CreateDirectory(logDirectory);
            }

            // 文件日志目标
            var fileTarget = new FileTarget("fileTarget")
            {
                FileName = Path.Combine(logDirectory, "app-${shortdate}.log"),
                Layout = "${longdate} ${level:uppercase=true} ${logger} ${message} ${exception:format=tostring}",
                Encoding = System.Text.Encoding.UTF8,
                MaxArchiveFiles = 7,
                ArchiveEvery = FileArchivePeriod.Day
            };

            // 控制台日志目标（调试时使用）
            var consoleTarget = new ConsoleTarget("consoleTarget")
            {
                Layout = "${time} ${level:uppercase=true} ${message} ${exception:format=tostring}"
            };

            // 添加规则
            config.AddTarget(fileTarget);
            config.AddTarget(consoleTarget);
            
            config.AddRule(LogLevel.Info, LogLevel.Fatal, fileTarget);
            
#if DEBUG
            config.AddRule(LogLevel.Debug, LogLevel.Fatal, consoleTarget);
#endif

            LogManager.Configuration = config;
        }

        private void SetupExceptionHandling()
        {
            // 处理UI线程未捕获的异常
            DispatcherUnhandledException += (sender, e) =>
            {
                Logger.Error(e.Exception, "UI线程未捕获异常");
                
                var result = MessageBox.Show(
                    $"应用程序发生错误：\n\n{e.Exception.Message}\n\n是否继续运行？",
                    "错误",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Error);
                
                if (result == MessageBoxResult.Yes)
                {
                    e.Handled = true;
                }
                else
                {
                    Current.Shutdown(1);
                }
            };

            // 处理非UI线程未捕获的异常
            AppDomain.CurrentDomain.UnhandledException += (sender, e) =>
            {
                Logger.Fatal(e.ExceptionObject as Exception, "应用程序域未捕获异常");
                
                if (e.IsTerminating)
                {
                    MessageBox.Show(
                        $"应用程序发生严重错误，即将退出：\n\n{(e.ExceptionObject as Exception)?.Message}",
                        "严重错误",
                        MessageBoxButton.OK,
                        MessageBoxImage.Error);
                }
            };

            // 处理Task未捕获的异常
            System.Threading.Tasks.TaskScheduler.UnobservedTaskException += (sender, e) =>
            {
                Logger.Error(e.Exception, "Task未捕获异常");
                e.SetObserved(); // 标记异常已被观察，防止应用程序崩溃
            };
        }
    }
}
