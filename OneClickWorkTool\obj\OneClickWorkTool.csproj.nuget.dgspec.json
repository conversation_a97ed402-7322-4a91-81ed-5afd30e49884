{"format": 1, "restore": {"D:\\一键工作\\OneClickWorkTool\\OneClickWorkTool.csproj": {}}, "projects": {"D:\\一键工作\\OneClickWorkTool\\OneClickWorkTool.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\一键工作\\OneClickWorkTool\\OneClickWorkTool.csproj", "projectName": "OneClickWorkTool", "projectPath": "D:\\一键工作\\OneClickWorkTool\\OneClickWorkTool.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\一键工作\\OneClickWorkTool\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0-windows7.0": {"targetAlias": "net9.0-windows", "dependencies": {"Microsoft.VisualBasic": {"target": "Package", "version": "[10.3.0, )"}, "NLog": {"target": "Package", "version": "[5.2.5, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "System.Management": {"target": "Package", "version": "[7.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.204/PortableRuntimeIdentifierGraph.json"}}}}}