using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;

namespace OneClickWorkTool.Models
{
    /// <summary>
    /// 数据文件配置（SData文件管理）
    /// </summary>
    public class DataFileConfig : INotifyPropertyChanged
    {
        private string _sourceDirectory = string.Empty;
        private bool _isEnabled = true;

        /// <summary>
        /// 源目录路径
        /// </summary>
        public string SourceDirectory
        {
            get => _sourceDirectory;
            set => SetProperty(ref _sourceDirectory, value);
        }

        /// <summary>
        /// 是否启用此配置
        /// </summary>
        public bool IsEnabled
        {
            get => _isEnabled;
            set => SetProperty(ref _isEnabled, value);
        }

        /// <summary>
        /// 目标路径列表
        /// </summary>
        public List<string> TargetPaths { get; set; } = new List<string>();

        /// <summary>
        /// 检测到的SData文件列表
        /// </summary>
        public List<SDataFileInfo> DetectedFiles { get; set; } = new List<SDataFileInfo>();

        /// <summary>
        /// 配置唯一标识
        /// </summary>
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// 配置名称
        /// </summary>
        public string Name { get; set; } = "数据文件配置";

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 最后扫描时间
        /// </summary>
        public DateTime LastScanTime { get; set; }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        /// <summary>
        /// 扫描源目录中的SData文件
        /// </summary>
        public void ScanSDataFiles()
        {
            DetectedFiles.Clear();

            if (string.IsNullOrWhiteSpace(SourceDirectory) || !Directory.Exists(SourceDirectory))
            {
                return;
            }

            try
            {
                // 递归搜索所有.SData文件
                var files = Directory.GetFiles(SourceDirectory, "*.SData", SearchOption.AllDirectories);
                
                foreach (var filePath in files)
                {
                    var fileInfo = new FileInfo(filePath);
                    var relativePath = Path.GetRelativePath(SourceDirectory, filePath);
                    
                    var sdataFile = new SDataFileInfo
                    {
                        FileName = fileInfo.Name,
                        RelativePath = relativePath,
                        FullPath = filePath,
                        FileSize = fileInfo.Length,
                        LastModified = fileInfo.LastWriteTime,
                        IsSelected = false // 默认不选中，由用户决定
                    };
                    
                    DetectedFiles.Add(sdataFile);
                }

                LastScanTime = DateTime.Now;
            }
            catch (Exception ex)
            {
                // 记录错误但不抛出异常
                System.Diagnostics.Debug.WriteLine($"扫描SData文件时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取选中的文件列表
        /// </summary>
        public List<SDataFileInfo> GetSelectedFiles()
        {
            return DetectedFiles.Where(f => f.IsSelected).ToList();
        }

        /// <summary>
        /// 验证配置是否有效
        /// </summary>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(SourceDirectory) &&
                   Directory.Exists(SourceDirectory) &&
                   TargetPaths.Any(path => !string.IsNullOrWhiteSpace(path));
        }
    }

    /// <summary>
    /// SData文件信息
    /// </summary>
    public class SDataFileInfo : INotifyPropertyChanged
    {
        private bool _isSelected;
        private List<string> _selectedTargetPaths = new List<string>();

        /// <summary>
        /// 文件名
        /// </summary>
        public string FileName { get; set; } = string.Empty;

        /// <summary>
        /// 相对路径
        /// </summary>
        public string RelativePath { get; set; } = string.Empty;

        /// <summary>
        /// 完整路径
        /// </summary>
        public string FullPath { get; set; } = string.Empty;

        /// <summary>
        /// 文件大小
        /// </summary>
        public long FileSize { get; set; }

        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime LastModified { get; set; }

        /// <summary>
        /// 是否选中此文件
        /// </summary>
        public bool IsSelected
        {
            get => _isSelected;
            set => SetProperty(ref _isSelected, value);
        }

        /// <summary>
        /// 选中的目标路径列表（此文件要复制到哪些目标路径）
        /// </summary>
        public List<string> SelectedTargetPaths
        {
            get => _selectedTargetPaths;
            set => SetProperty(ref _selectedTargetPaths, value);
        }

        /// <summary>
        /// 文件大小显示文本
        /// </summary>
        public string FileSizeText
        {
            get
            {
                if (FileSize < 1024)
                    return $"{FileSize} B";
                else if (FileSize < 1024 * 1024)
                    return $"{FileSize / 1024.0:F1} KB";
                else if (FileSize < 1024 * 1024 * 1024)
                    return $"{FileSize / (1024.0 * 1024):F1} MB";
                else
                    return $"{FileSize / (1024.0 * 1024 * 1024):F1} GB";
            }
        }

        /// <summary>
        /// 最后修改时间显示文本
        /// </summary>
        public string LastModifiedText => LastModified.ToString("yyyy-MM-dd HH:mm:ss");

        /// <summary>
        /// 目标路径显示文本
        /// </summary>
        public string TargetPathsText
        {
            get
            {
                if (!SelectedTargetPaths.Any())
                    return "未选择目标";
                
                if (SelectedTargetPaths.Count == 1)
                    return Path.GetFileName(SelectedTargetPaths[0]);
                
                return $"{SelectedTargetPaths.Count}个目标";
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            
            if (propertyName == nameof(SelectedTargetPaths))
            {
                OnPropertyChanged(nameof(TargetPathsText));
            }
            
            return true;
        }
    }
}
