﻿#pragma checksum "..\..\..\..\Views\ProjectConfigWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "F2878FBAD1A88D3A2E7611E8B8F8E16653E5A027"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace OneClickWorkTool.Views {
    
    
    /// <summary>
    /// ProjectConfigWindow
    /// </summary>
    public partial class ProjectConfigWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 61 "..\..\..\..\Views\ProjectConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ProjectNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 69 "..\..\..\..\Views\ProjectConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ProjectPathTextBox;
        
        #line default
        #line hidden
        
        
        #line 70 "..\..\..\..\Views\ProjectConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BrowseProjectPathButton;
        
        #line default
        #line hidden
        
        
        #line 93 "..\..\..\..\Views\ProjectConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SolutionPathTextBox;
        
        #line default
        #line hidden
        
        
        #line 94 "..\..\..\..\Views\ProjectConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BrowseSolutionPathButton;
        
        #line default
        #line hidden
        
        
        #line 98 "..\..\..\..\Views\ProjectConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox BuildConfigComboBox;
        
        #line default
        #line hidden
        
        
        #line 101 "..\..\..\..\Views\ProjectConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox BuildPlatformComboBox;
        
        #line default
        #line hidden
        
        
        #line 124 "..\..\..\..\Views\ProjectConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox FrontendExePathTextBox;
        
        #line default
        #line hidden
        
        
        #line 125 "..\..\..\..\Views\ProjectConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BrowseFrontendExeButton;
        
        #line default
        #line hidden
        
        
        #line 129 "..\..\..\..\Views\ProjectConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox FrontendArgsTextBox;
        
        #line default
        #line hidden
        
        
        #line 137 "..\..\..\..\Views\ProjectConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BackendExePathTextBox;
        
        #line default
        #line hidden
        
        
        #line 138 "..\..\..\..\Views\ProjectConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BrowseBackendExeButton;
        
        #line default
        #line hidden
        
        
        #line 142 "..\..\..\..\Views\ProjectConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BackendArgsTextBox;
        
        #line default
        #line hidden
        
        
        #line 164 "..\..\..\..\Views\ProjectConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DataTableSourcePathTextBox;
        
        #line default
        #line hidden
        
        
        #line 165 "..\..\..\..\Views\ProjectConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BrowseDataTableSourceButton;
        
        #line default
        #line hidden
        
        
        #line 170 "..\..\..\..\Views\ProjectConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox DataTableTargetPathsListBox;
        
        #line default
        #line hidden
        
        
        #line 172 "..\..\..\..\Views\ProjectConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddTargetPathButton;
        
        #line default
        #line hidden
        
        
        #line 173 "..\..\..\..\Views\ProjectConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RemoveTargetPathButton;
        
        #line default
        #line hidden
        
        
        #line 183 "..\..\..\..\Views\ProjectConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PackageOutputPathTextBox;
        
        #line default
        #line hidden
        
        
        #line 184 "..\..\..\..\Views\ProjectConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BrowsePackageOutputButton;
        
        #line default
        #line hidden
        
        
        #line 192 "..\..\..\..\Views\ProjectConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OkButton;
        
        #line default
        #line hidden
        
        
        #line 193 "..\..\..\..\Views\ProjectConfigWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/OneClickWorkTool;V1.0.0.0;component/views/projectconfigwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\ProjectConfigWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.ProjectNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 2:
            this.ProjectPathTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.BrowseProjectPathButton = ((System.Windows.Controls.Button)(target));
            
            #line 70 "..\..\..\..\Views\ProjectConfigWindow.xaml"
            this.BrowseProjectPathButton.Click += new System.Windows.RoutedEventHandler(this.BrowseProjectPathButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.SolutionPathTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.BrowseSolutionPathButton = ((System.Windows.Controls.Button)(target));
            
            #line 94 "..\..\..\..\Views\ProjectConfigWindow.xaml"
            this.BrowseSolutionPathButton.Click += new System.Windows.RoutedEventHandler(this.BrowseSolutionPathButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.BuildConfigComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 7:
            this.BuildPlatformComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 8:
            this.FrontendExePathTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 9:
            this.BrowseFrontendExeButton = ((System.Windows.Controls.Button)(target));
            
            #line 125 "..\..\..\..\Views\ProjectConfigWindow.xaml"
            this.BrowseFrontendExeButton.Click += new System.Windows.RoutedEventHandler(this.BrowseFrontendExeButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.FrontendArgsTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 11:
            this.BackendExePathTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            this.BrowseBackendExeButton = ((System.Windows.Controls.Button)(target));
            
            #line 138 "..\..\..\..\Views\ProjectConfigWindow.xaml"
            this.BrowseBackendExeButton.Click += new System.Windows.RoutedEventHandler(this.BrowseBackendExeButton_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.BackendArgsTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 14:
            this.DataTableSourcePathTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 15:
            this.BrowseDataTableSourceButton = ((System.Windows.Controls.Button)(target));
            
            #line 165 "..\..\..\..\Views\ProjectConfigWindow.xaml"
            this.BrowseDataTableSourceButton.Click += new System.Windows.RoutedEventHandler(this.BrowseDataTableSourceButton_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.DataTableTargetPathsListBox = ((System.Windows.Controls.ListBox)(target));
            return;
            case 17:
            this.AddTargetPathButton = ((System.Windows.Controls.Button)(target));
            
            #line 172 "..\..\..\..\Views\ProjectConfigWindow.xaml"
            this.AddTargetPathButton.Click += new System.Windows.RoutedEventHandler(this.AddTargetPathButton_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            this.RemoveTargetPathButton = ((System.Windows.Controls.Button)(target));
            
            #line 173 "..\..\..\..\Views\ProjectConfigWindow.xaml"
            this.RemoveTargetPathButton.Click += new System.Windows.RoutedEventHandler(this.RemoveTargetPathButton_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.PackageOutputPathTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 20:
            this.BrowsePackageOutputButton = ((System.Windows.Controls.Button)(target));
            
            #line 184 "..\..\..\..\Views\ProjectConfigWindow.xaml"
            this.BrowsePackageOutputButton.Click += new System.Windows.RoutedEventHandler(this.BrowsePackageOutputButton_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            this.OkButton = ((System.Windows.Controls.Button)(target));
            
            #line 192 "..\..\..\..\Views\ProjectConfigWindow.xaml"
            this.OkButton.Click += new System.Windows.RoutedEventHandler(this.OkButton_Click);
            
            #line default
            #line hidden
            return;
            case 22:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 193 "..\..\..\..\Views\ProjectConfigWindow.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

