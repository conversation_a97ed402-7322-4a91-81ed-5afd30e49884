# 一键工作工具 - 安装和使用指南

## 系统要求

### 操作系统
- Windows 10 或更高版本
- Windows Server 2016 或更高版本

### 开发环境要求
- .NET 6.0 SDK 或更高版本
- Visual Studio 2022 或 Visual Studio Code（推荐）
- Visual Studio 2008（用于C++项目编译）

## 安装步骤

### 1. 安装 .NET 6.0 SDK

#### 方法一：从官网下载
1. 访问 [https://dotnet.microsoft.com/download/dotnet/6.0](https://dotnet.microsoft.com/download/dotnet/6.0)
2. 下载 ".NET 6.0 SDK" (不是Runtime)
3. 运行安装程序并按照提示完成安装

#### 方法二：使用包管理器
```powershell
# 使用 Chocolatey
choco install dotnet-6.0-sdk

# 使用 Winget
winget install Microsoft.DotNet.SDK.6
```

#### 验证安装
打开命令提示符或PowerShell，运行：
```bash
dotnet --version
```
应该显示类似 `6.0.xxx` 的版本号。

### 2. 安装开发工具（可选）

#### Visual Studio 2022 Community（推荐）
1. 访问 [https://visualstudio.microsoft.com/vs/community/](https://visualstudio.microsoft.com/vs/community/)
2. 下载并安装 Visual Studio 2022 Community
3. 在安装时选择 ".NET 桌面开发" 工作负载

#### Visual Studio Code
1. 访问 [https://code.visualstudio.com/](https://code.visualstudio.com/)
2. 下载并安装 VS Code
3. 安装以下扩展：
   - C# for Visual Studio Code
   - .NET Install Tool for Extension Authors

## 编译和运行项目

### 方法一：使用构建脚本（推荐）
1. 双击运行 `build.bat` 文件
2. 选择相应的操作：
   - 选择 `1` 还原依赖包
   - 选择 `3` 编译项目（Release版本）
   - 选择 `4` 运行项目

### 方法二：使用命令行
```bash
# 进入项目目录
cd OneClickWorkTool

# 还原依赖包
dotnet restore

# 编译项目
dotnet build -c Release

# 运行项目
dotnet run
```

### 方法三：使用Visual Studio
1. 打开 `OneClickWorkTool.sln` 文件（如果存在）
2. 或者打开 `OneClickWorkTool/OneClickWorkTool.csproj` 文件
3. 按 F5 运行项目

## 发布可执行文件

### 创建独立可执行文件
```bash
cd OneClickWorkTool
dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true -o ../bin
```

这将在 `bin` 目录下生成 `OneClickWorkTool.exe` 文件，可以在没有安装.NET的机器上运行。

## 配置Visual Studio 2008路径

首次运行时，需要配置VS2008的安装路径：

1. 启动应用程序
2. 点击"设置"按钮
3. 在应用程序配置中设置VS2008路径，通常为：
   ```
   C:\Program Files (x86)\Microsoft Visual Studio 9.0\Common7\IDE\devenv.exe
   ```

## 常见问题解决

### 问题1：提示找不到 .NET 运行时
**解决方案**：确保已安装 .NET 6.0 SDK，而不仅仅是Runtime。

### 问题2：编译时提示缺少依赖
**解决方案**：
```bash
cd OneClickWorkTool
dotnet restore
dotnet clean
dotnet build
```

### 问题3：无法启动VS2008编译
**解决方案**：
1. 确保VS2008已正确安装
2. 检查VS2008路径配置是否正确
3. 确保有足够的权限访问VS2008

### 问题4：进程启动失败
**解决方案**：
1. 检查可执行文件路径是否正确
2. 确保可执行文件存在且有执行权限
3. 检查工作目录是否正确

### 问题5：配置文件损坏
**解决方案**：
删除配置文件让程序重新创建：
```
%USERPROFILE%\Documents\OneClickWorkTool\projects.json
%USERPROFILE%\Documents\OneClickWorkTool\app.config.json
```

## 日志文件位置

应用程序日志保存在：
```
%USERPROFILE%\Documents\OneClickWorkTool\Logs\
```

如果遇到问题，可以查看日志文件获取详细错误信息。

## 技术支持

如果遇到其他问题：

1. 查看日志文件获取详细错误信息
2. 检查 GitHub Issues 是否有类似问题
3. 提交新的 Issue 并附上：
   - 操作系统版本
   - .NET 版本 (`dotnet --version`)
   - 错误信息和日志
   - 重现步骤

## 更新说明

### 检查更新
定期检查项目仓库获取最新版本和功能更新。

### 升级步骤
1. 备份当前配置文件
2. 下载新版本源代码
3. 重新编译和安装
4. 恢复配置文件（如果兼容）

---

**注意**：本工具专为Windows平台设计，需要Visual Studio 2008支持C++项目编译功能。
