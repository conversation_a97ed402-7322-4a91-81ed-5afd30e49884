{"dotnet.defaultSolution": "OneClickWorkTool/OneClickWorkTool.csproj", "files.encoding": "utf8", "files.associations": {"*.cs": "csharp", "*.xaml": "xml"}, "omnisharp.enableEditorConfigSupport": true, "omnisharp.enableImportCompletion": true, "omnisharp.enableRoslynAnalyzers": true, "csharp.semanticHighlighting.enabled": true, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll": "explicit"}}