﻿#pragma checksum "..\..\..\..\Views\DataFileManagerWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "75EB6643CC65126B5F9C0816CAAFEBA3A7DB64D4"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace OneClickWorkTool.Views {
    
    
    /// <summary>
    /// DataFileManagerWindow
    /// </summary>
    public partial class DataFileManagerWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 49 "..\..\..\..\Views\DataFileManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SourcePathTextBox;
        
        #line default
        #line hidden
        
        
        #line 50 "..\..\..\..\Views\DataFileManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BrowseSourceButton;
        
        #line default
        #line hidden
        
        
        #line 52 "..\..\..\..\Views\DataFileManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ScanFilesButton;
        
        #line default
        #line hidden
        
        
        #line 66 "..\..\..\..\Views\DataFileManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox TargetPathsListBox;
        
        #line default
        #line hidden
        
        
        #line 68 "..\..\..\..\Views\DataFileManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddTargetPathButton;
        
        #line default
        #line hidden
        
        
        #line 70 "..\..\..\..\Views\DataFileManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RemoveTargetPathButton;
        
        #line default
        #line hidden
        
        
        #line 74 "..\..\..\..\Views\DataFileManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SelectAllFilesButton;
        
        #line default
        #line hidden
        
        
        #line 76 "..\..\..\..\Views\DataFileManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button UnselectAllFilesButton;
        
        #line default
        #line hidden
        
        
        #line 85 "..\..\..\..\Views\DataFileManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FileCountText;
        
        #line default
        #line hidden
        
        
        #line 86 "..\..\..\..\Views\DataFileManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SelectedCountText;
        
        #line default
        #line hidden
        
        
        #line 87 "..\..\..\..\Views\DataFileManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LastScanTimeText;
        
        #line default
        #line hidden
        
        
        #line 91 "..\..\..\..\Views\DataFileManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid SDataFilesDataGrid;
        
        #line default
        #line hidden
        
        
        #line 118 "..\..\..\..\Views\DataFileManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CopySelectedButton;
        
        #line default
        #line hidden
        
        
        #line 120 "..\..\..\..\Views\DataFileManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshButton;
        
        #line default
        #line hidden
        
        
        #line 122 "..\..\..\..\Views\DataFileManagerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/OneClickWorkTool;V1.0.0.0;component/views/datafilemanagerwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\DataFileManagerWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.SourcePathTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 2:
            this.BrowseSourceButton = ((System.Windows.Controls.Button)(target));
            
            #line 51 "..\..\..\..\Views\DataFileManagerWindow.xaml"
            this.BrowseSourceButton.Click += new System.Windows.RoutedEventHandler(this.BrowseSourceButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.ScanFilesButton = ((System.Windows.Controls.Button)(target));
            
            #line 53 "..\..\..\..\Views\DataFileManagerWindow.xaml"
            this.ScanFilesButton.Click += new System.Windows.RoutedEventHandler(this.ScanFilesButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.TargetPathsListBox = ((System.Windows.Controls.ListBox)(target));
            return;
            case 5:
            this.AddTargetPathButton = ((System.Windows.Controls.Button)(target));
            
            #line 69 "..\..\..\..\Views\DataFileManagerWindow.xaml"
            this.AddTargetPathButton.Click += new System.Windows.RoutedEventHandler(this.AddTargetPathButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.RemoveTargetPathButton = ((System.Windows.Controls.Button)(target));
            
            #line 71 "..\..\..\..\Views\DataFileManagerWindow.xaml"
            this.RemoveTargetPathButton.Click += new System.Windows.RoutedEventHandler(this.RemoveTargetPathButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.SelectAllFilesButton = ((System.Windows.Controls.Button)(target));
            
            #line 75 "..\..\..\..\Views\DataFileManagerWindow.xaml"
            this.SelectAllFilesButton.Click += new System.Windows.RoutedEventHandler(this.SelectAllFilesButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.UnselectAllFilesButton = ((System.Windows.Controls.Button)(target));
            
            #line 77 "..\..\..\..\Views\DataFileManagerWindow.xaml"
            this.UnselectAllFilesButton.Click += new System.Windows.RoutedEventHandler(this.UnselectAllFilesButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.FileCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.SelectedCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.LastScanTimeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.SDataFilesDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 14:
            this.CopySelectedButton = ((System.Windows.Controls.Button)(target));
            
            #line 119 "..\..\..\..\Views\DataFileManagerWindow.xaml"
            this.CopySelectedButton.Click += new System.Windows.RoutedEventHandler(this.CopySelectedButton_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.RefreshButton = ((System.Windows.Controls.Button)(target));
            
            #line 121 "..\..\..\..\Views\DataFileManagerWindow.xaml"
            this.RefreshButton.Click += new System.Windows.RoutedEventHandler(this.RefreshButton_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.CloseButton = ((System.Windows.Controls.Button)(target));
            
            #line 123 "..\..\..\..\Views\DataFileManagerWindow.xaml"
            this.CloseButton.Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 13:
            
            #line 108 "..\..\..\..\Views\DataFileManagerWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SelectTargetPathsButton_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

