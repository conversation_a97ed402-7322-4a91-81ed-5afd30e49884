using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using OneClickWorkTool.Models;
using NLog;

namespace OneClickWorkTool.Services
{
    /// <summary>
    /// 编译服务
    /// </summary>
    public class BuildService
    {
        private static readonly Logger Logger = LogManager.GetCurrentClassLogger();
        private readonly string _vs2008Path;
        private Process? _buildProcess;
        private CancellationTokenSource? _cancellationTokenSource;

        public BuildService(string vs2008Path)
        {
            _vs2008Path = vs2008Path;
        }

        /// <summary>
        /// 编译状态改变事件
        /// </summary>
        public event EventHandler<BuildInfo>? BuildStatusChanged;

        /// <summary>
        /// 编译输出事件
        /// </summary>
        public event EventHandler<string>? BuildOutputReceived;

        /// <summary>
        /// 开始编译
        /// </summary>
        public async Task<bool> StartBuildAsync(ProjectConfig project, BuildInfo buildInfo)
        {
            if (project == null)
                throw new ArgumentNullException(nameof(project));

            if (buildInfo == null)
                throw new ArgumentNullException(nameof(buildInfo));

            if (string.IsNullOrWhiteSpace(project.SolutionPath) || !File.Exists(project.SolutionPath))
            {
                Logger.Error($"解决方案文件不存在: {project.SolutionPath}");
                return false;
            }

            if (!File.Exists(_vs2008Path))
            {
                Logger.Error($"Visual Studio 2008 不存在: {_vs2008Path}");
                return false;
            }

            try
            {
                buildInfo.StartBuild();
                OnBuildStatusChanged(buildInfo);

                _cancellationTokenSource = new CancellationTokenSource();
                var result = await BuildSolutionAsync(project, buildInfo, _cancellationTokenSource.Token);

                buildInfo.CompleteBuild(result);
                OnBuildStatusChanged(buildInfo);

                return result;
            }
            catch (OperationCanceledException)
            {
                buildInfo.Status = BuildStatus.Cancelled;
                OnBuildStatusChanged(buildInfo);
                Logger.Info("编译已取消");
                return false;
            }
            catch (Exception ex)
            {
                buildInfo.Status = BuildStatus.Failed;
                buildInfo.Output += $"\n错误: {ex.Message}";
                OnBuildStatusChanged(buildInfo);
                Logger.Error(ex, "编译过程中发生错误");
                return false;
            }
        }

        /// <summary>
        /// 取消编译
        /// </summary>
        public void CancelBuild()
        {
            try
            {
                _cancellationTokenSource?.Cancel();

                if (_buildProcess != null && !_buildProcess.HasExited)
                {
                    _buildProcess.Kill();
                    Logger.Info("编译进程已终止");
                }
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "取消编译时发生错误");
            }
        }

        /// <summary>
        /// 检测可用的编译配置
        /// </summary>
        public async Task<(string[] configurations, string[] platforms)> DetectBuildConfigurationsAsync(string solutionPath)
        {
            if (!File.Exists(solutionPath))
            {
                Logger.Warning($"解决方案文件不存在: {solutionPath}");
                return (new[] { "Debug", "Release" }, new[] { "Win32", "x64" });
            }

            try
            {
                var content = await File.ReadAllTextAsync(solutionPath);
                var configurations = new HashSet<string>();
                var platforms = new HashSet<string>();

                // 解析解决方案文件中的配置信息
                var lines = content.Split('\n');
                bool inConfigSection = false;

                foreach (var line in lines)
                {
                    var trimmedLine = line.Trim();

                    // 检测配置段落开始
                    if (trimmedLine.StartsWith("GlobalSection(SolutionConfigurationPlatforms)"))
                    {
                        inConfigSection = true;
                        Logger.Info("找到解决方案配置段落");
                        continue;
                    }

                    // 检测配置段落结束
                    if (inConfigSection && trimmedLine.StartsWith("EndGlobalSection"))
                    {
                        inConfigSection = false;
                        break;
                    }

                    // 解析配置行
                    if (inConfigSection && trimmedLine.Contains("|") && trimmedLine.Contains("="))
                    {
                        // 格式: Debug|Win32 = Debug|Win32
                        var configPart = trimmedLine.Split('=')[0].Trim();
                        var parts = configPart.Split('|');

                        if (parts.Length == 2)
                        {
                            var config = parts[0].Trim();
                            var platform = parts[1].Trim();

                            configurations.Add(config);
                            platforms.Add(platform);

                            Logger.Debug($"检测到配置: {config}|{platform}");
                        }
                    }
                }

                Logger.Info($"检测到 {configurations.Count} 个编译配置，{platforms.Count} 个平台");

                // 如果没有找到配置，使用默认值
                if (configurations.Count == 0)
                {
                    Logger.Warning("未检测到编译配置，使用默认值");
                    configurations.Add("Debug");
                    configurations.Add("Release");
                }

                if (platforms.Count == 0)
                {
                    Logger.Warning("未检测到编译平台，使用默认值");
                    platforms.Add("Win32");
                    platforms.Add("x64");
                }

                var configArray = configurations.OrderBy(c => c).ToArray();
                var platformArray = platforms.OrderBy(p => p).ToArray();

                Logger.Info($"最终配置列表: [{string.Join(", ", configArray)}]");
                Logger.Info($"最终平台列表: [{string.Join(", ", platformArray)}]");

                return (configArray, platformArray);
            }
            catch (Exception ex)
            {
                Logger.Error(ex, $"检测编译配置时发生错误: {solutionPath}");
                return (new[] { "Debug", "Release" }, new[] { "Win32", "x64" });
            }
        }

        private async Task<bool> BuildSolutionAsync(ProjectConfig project, BuildInfo buildInfo, CancellationToken cancellationToken)
        {
            var configPlatform = $"{buildInfo.Configuration}|{buildInfo.Platform}";
            var arguments = $"\"{project.SolutionPath}\" /build \"{configPlatform}\"";

            Logger.Info($"开始编译: {_vs2008Path} {arguments}");

            var startInfo = new ProcessStartInfo
            {
                FileName = _vs2008Path,
                Arguments = arguments,
                UseShellExecute = false,
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                CreateNoWindow = true,
                StandardOutputEncoding = Encoding.UTF8,
                StandardErrorEncoding = Encoding.UTF8
            };

            _buildProcess = new Process { StartInfo = startInfo };

            var outputBuilder = new StringBuilder();
            var errorBuilder = new StringBuilder();

            _buildProcess.OutputDataReceived += (sender, e) =>
            {
                if (!string.IsNullOrEmpty(e.Data))
                {
                    outputBuilder.AppendLine(e.Data);
                    buildInfo.Output = outputBuilder.ToString();
                    OnBuildOutputReceived(e.Data);

                    // 解析编译输出，更新错误和警告计数
                    ParseBuildOutput(e.Data, buildInfo);
                }
            };

            _buildProcess.ErrorDataReceived += (sender, e) =>
            {
                if (!string.IsNullOrEmpty(e.Data))
                {
                    errorBuilder.AppendLine(e.Data);
                    buildInfo.Output += $"\nERROR: {e.Data}";
                    OnBuildOutputReceived($"ERROR: {e.Data}");
                }
            };

            _buildProcess.Start();
            _buildProcess.BeginOutputReadLine();
            _buildProcess.BeginErrorReadLine();

            // 等待编译完成或取消
            while (!_buildProcess.HasExited)
            {
                if (cancellationToken.IsCancellationRequested)
                {
                    _buildProcess.Kill();
                    throw new OperationCanceledException();
                }

                await Task.Delay(100, cancellationToken);
            }

            var exitCode = _buildProcess.ExitCode;
            var success = exitCode == 0;

            Logger.Info($"编译完成，退出代码: {exitCode}，成功: {success}");

            return success;
        }

        private void ParseBuildOutput(string output, BuildInfo buildInfo)
        {
            // 简单的输出解析，统计错误和警告
            if (output.Contains("error") || output.Contains("Error") || output.Contains("ERROR"))
            {
                buildInfo.ErrorCount++;
            }

            if (output.Contains("warning") || output.Contains("Warning") || output.Contains("WARNING"))
            {
                buildInfo.WarningCount++;
            }

            // 尝试解析编译进度（这个比较困难，VS2008的输出格式不太标准）
            // 这里只是一个简单的估算
            if (output.Contains("Building") || output.Contains("Compiling"))
            {
                buildInfo.Progress = Math.Min(buildInfo.Progress + 5, 95);
            }
        }

        private void OnBuildStatusChanged(BuildInfo buildInfo)
        {
            BuildStatusChanged?.Invoke(this, buildInfo);
        }

        private void OnBuildOutputReceived(string output)
        {
            BuildOutputReceived?.Invoke(this, output);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _cancellationTokenSource?.Cancel();
            _cancellationTokenSource?.Dispose();
            _buildProcess?.Dispose();
        }
    }
}
