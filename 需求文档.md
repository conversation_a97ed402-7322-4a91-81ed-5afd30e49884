# 一键工作工具需求文档

## 项目概述

### 目标用户
C++游戏开发程序员，主要使用Visual Studio 2008进行游戏项目前后端开发

### 核心需求
开发一个集成化的工作流管理工具，简化日常开发、编译、测试、打包等重复性工作流程

## 功能需求详细说明

### 1. 项目管理模块

#### 1.1 多项目支持
- **功能描述**: 支持同时管理多个游戏项目工程
- **技术要求**:
  - 项目配置文件存储（JSON/XML格式）
  - 项目列表持久化存储
  - 项目快速切换机制

#### 1.2 项目切换界面
- **UI设计**: 
  - 顶部标签页或左右箭头切换设计
  - 当前项目名称高亮显示
  - 支持项目重命名和删除
- **交互逻辑**:
  - 左右箭头键盘快捷键支持
  - 鼠标点击切换
  - 项目状态保存（最后打开的项目自动恢复）

#### 1.3 项目配置管理
- **配置项包括**:
  - 项目根路径
  - VS2008解决方案文件路径(.sln)
  - 前端可执行文件路径
  - 后端可执行文件路径
  - 导表工具输出路径
  - 目标复制路径列表
  - 打包输出路径

### 2. 编译管理模块

#### 2.1 VS2008集成编译
- **技术实现**:
  - 通过COM接口或命令行调用VS2008编译功能
  - 使用devenv.exe命令行工具: `devenv solution.sln /build "Release|Win32"`
  - 支持的编译配置自动检测

#### 2.2 编译配置管理
- **功能要求**:
  - 自动读取解决方案文件中的可用配置（Release、Debug等）
  - 平台配置检测（Win32、x64等）
  - 编译配置下拉选择框
  - 默认配置记忆功能

#### 2.3 编译状态监控
- **实现方案**:
  - 实时监控编译进程状态
  - 编译输出日志显示
  - 编译成功/失败状态提示
  - 编译时间统计
  - 错误信息高亮显示

### 3. 进程管理模块

#### 3.1 前后端进程启动
- **配置要求**:
  - 前端可执行文件路径配置
  - 后端可执行文件路径配置
  - 启动参数配置（命令行参数）
  - 工作目录配置

#### 3.2 进程控制功能
- **启动功能**:
  - 单独启动前端/后端
  - 一键启动前后端
  - 进程启动状态检测
  
- **关闭功能**:
  - 优雅关闭（发送关闭信号）
  - 强制终止进程
  - 批量关闭功能
  - 自定义关闭脚本支持

#### 3.3 进程状态监控
- **监控内容**:
  - 进程运行状态（运行中/已停止）
  - 进程ID显示
  - CPU和内存使用率
  - 运行时长统计

### 4. 文件管理模块

#### 4.1 导表文件管理
- **功能描述**: 将导表工具输出的文件一键复制到项目指定位置
- **技术实现**:
  - 源路径：导表工具输出目录
  - 目标路径：支持配置多个目标路径
  - 文件过滤：支持按文件类型过滤
  - 覆盖策略：提供覆盖确认机制

#### 4.2 可执行文件打包
- **打包流程**:
  1. 从编译输出目录复制可执行文件
  2. 复制相关依赖文件（DLL等）
  3. 创建压缩包（ZIP/RAR格式）
  4. 自动命名（项目名+版本+时间戳）

#### 4.3 文件操作界面
- **UI设计**:
  - 源文件路径显示
  - 目标路径列表（可多选）
  - 操作按钮（复制、打包、压缩）
  - 操作进度条
  - 操作日志显示

### 5. VS2008调试集成模块

#### 5.1 进程附加功能
- **技术方案**:
  - 通过VS2008 DTE（Development Tools Environment）接口实现
  - 或使用VS命令行工具实现进程附加

#### 5.2 VS实例管理
- **功能要求**:
  - 检测当前运行的VS2008实例
  - 显示VS实例列表（进程ID、项目名称）
  - 支持选择特定VS实例进行附加

#### 5.3 进程绑定配置
- **配置界面**:
  - VS实例选择下拉框
  - 可附加进程列表（前端、后端进程）
  - 进程选择复选框（支持多选）
  - 一键附加按钮

### 6. 用户界面设计

#### 6.1 主界面布局
```
┌─────────────────────────────────────────┐
│ 项目切换区域 [← 项目A ↔ 项目B ↔ 项目C →] │
├─────────────────────────────────────────┤
│ 编译区域                                │
│ [配置选择▼] [编译按钮] [状态显示]       │
├─────────────────────────────────────────┤
│ 进程管理区域                            │
│ [启动前端] [启动后端] [关闭进程]        │
│ [进程状态显示区域]                      │
├─────────────────────────────────────────┤
│ 文件管理区域                            │
│ [导表复制] [文件打包] [VS附加]          │
├─────────────────────────────────────────┤
│ 日志输出区域                            │
└─────────────────────────────────────────┘
```

#### 6.2 设置界面设计
- **路径配置页面**:
  - 项目根路径浏览选择
  - 各种文件路径配置
  - 路径有效性验证
  
- **编译配置页面**:
  - VS2008路径配置
  - 编译参数设置
  
- **进程配置页面**:
  - 可执行文件路径配置
  - 启动参数配置
  - 关闭脚本配置

### 7. 技术实现建议

#### 7.1 开发技术栈
- **推荐方案1**: C# WinForms/WPF
  - 优势：与VS2008 COM接口集成容易
  - 适合Windows平台开发
  
- **推荐方案2**: C++ Qt
  - 优势：性能好，界面美观
  - 与现有C++技术栈匹配

#### 7.2 关键技术点
- **VS2008集成**: 使用EnvDTE接口
- **进程管理**: System.Diagnostics.Process类
- **文件操作**: 异步文件复制，进度回调
- **配置管理**: JSON/XML序列化
- **界面响应**: 多线程处理，避免UI阻塞

#### 7.3 第三方库建议
- **压缩功能**: SharpZipLib 或 7-Zip
- **配置管理**: Newtonsoft.Json
- **日志记录**: NLog 或 Serilog
- **UI框架**: DevExpress 或 Telerik（可选）

### 8. 非功能性需求

#### 8.1 性能要求
- 工具启动时间 < 3秒
- 编译状态检测延迟 < 1秒
- 文件复制支持大文件（>100MB）

#### 8.2 可靠性要求
- 异常处理机制完善
- 配置文件损坏恢复
- 进程崩溃自动检测

#### 8.3 易用性要求
- 界面简洁直观
- 操作步骤最小化
- 快捷键支持
- 操作撤销功能

### 9. 开发计划建议

#### 阶段1：核心功能开发（2-3周）
- 项目管理基础功能
- 编译集成功能
- 基础UI框架

#### 阶段2：进程管理开发（1-2周）
- 进程启动/关闭功能
- 进程状态监控

#### 阶段3：文件管理开发（1-2周）
- 文件复制功能
- 打包压缩功能

#### 阶段4：VS集成开发（1-2周）
- VS2008进程附加功能
- 调试集成

#### 阶段5：界面优化和测试（1周）
- UI美化
- 功能测试
- 性能优化

### 10. 风险评估

#### 10.1 技术风险
- VS2008 COM接口兼容性问题
- 不同Windows版本兼容性
- 进程权限问题

#### 10.2 解决方案
- 提供多种VS集成方案备选
- 充分测试不同环境
- 管理员权限运行选项

这个需求文档为开发人员提供了详细的功能规格说明和技术实现建议，可以作为开发此工具的完整指南。
