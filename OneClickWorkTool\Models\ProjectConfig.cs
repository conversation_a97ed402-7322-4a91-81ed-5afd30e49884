using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace OneClickWorkTool.Models
{
    /// <summary>
    /// 项目配置数据模型
    /// </summary>
    public class ProjectConfig : INotifyPropertyChanged
    {
        private string _name = string.Empty;
        private string _projectPath = string.Empty;
        private string _solutionPath = string.Empty;
        private string _frontendExePath = string.Empty;
        private string _backendExePath = string.Empty;
        private string _dataTableSourcePath = string.Empty;
        private string _buildConfiguration = "Release";
        private string _buildPlatform = "Win32";

        /// <summary>
        /// 项目名称
        /// </summary>
        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        /// <summary>
        /// 项目根路径
        /// </summary>
        public string ProjectPath
        {
            get => _projectPath;
            set => SetProperty(ref _projectPath, value);
        }

        /// <summary>
        /// VS2008解决方案文件路径
        /// </summary>
        public string SolutionPath
        {
            get => _solutionPath;
            set => SetProperty(ref _solutionPath, value);
        }

        /// <summary>
        /// 前端可执行文件路径
        /// </summary>
        public string FrontendExePath
        {
            get => _frontendExePath;
            set => SetProperty(ref _frontendExePath, value);
        }

        /// <summary>
        /// 后端可执行文件路径
        /// </summary>
        public string BackendExePath
        {
            get => _backendExePath;
            set => SetProperty(ref _backendExePath, value);
        }

        /// <summary>
        /// 导表工具输出路径
        /// </summary>
        public string DataTableSourcePath
        {
            get => _dataTableSourcePath;
            set => SetProperty(ref _dataTableSourcePath, value);
        }

        /// <summary>
        /// 编译配置（Release/Debug等）
        /// </summary>
        public string BuildConfiguration
        {
            get => _buildConfiguration;
            set => SetProperty(ref _buildConfiguration, value);
        }

        /// <summary>
        /// 编译平台（Win32/x64等）
        /// </summary>
        public string BuildPlatform
        {
            get => _buildPlatform;
            set => SetProperty(ref _buildPlatform, value);
        }

        /// <summary>
        /// 导表文件目标路径列表
        /// </summary>
        public List<string> DataTableTargetPaths { get; set; } = new List<string>();

        /// <summary>
        /// 打包输出路径
        /// </summary>
        public string PackageOutputPath { get; set; } = string.Empty;

        /// <summary>
        /// 前端启动参数
        /// </summary>
        public string FrontendStartupArgs { get; set; } = string.Empty;

        /// <summary>
        /// 后端启动参数
        /// </summary>
        public string BackendStartupArgs { get; set; } = string.Empty;

        /// <summary>
        /// 前端关闭脚本路径
        /// </summary>
        public string FrontendCloseScriptPath { get; set; } = string.Empty;

        /// <summary>
        /// 后端关闭脚本路径
        /// </summary>
        public string BackendCloseScriptPath { get; set; } = string.Empty;

        /// <summary>
        /// 可用的编译配置列表
        /// </summary>
        public List<string> AvailableBuildConfigurations { get; set; } = new List<string> { "Debug", "Release" };

        /// <summary>
        /// 可用的编译平台列表
        /// </summary>
        public List<string> AvailableBuildPlatforms { get; set; } = new List<string> { "Win32", "x64" };

        /// <summary>
        /// 项目唯一标识
        /// </summary>
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime LastModifiedTime { get; set; } = DateTime.Now;

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value)) return false;
            field = value;
            LastModifiedTime = DateTime.Now;
            OnPropertyChanged(propertyName);
            return true;
        }

        /// <summary>
        /// 验证项目配置是否有效
        /// </summary>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(Name) &&
                   !string.IsNullOrWhiteSpace(ProjectPath) &&
                   System.IO.Directory.Exists(ProjectPath);
        }

        /// <summary>
        /// 获取项目显示名称
        /// </summary>
        public string DisplayName => string.IsNullOrWhiteSpace(Name) ? "未命名项目" : Name;
    }
}
