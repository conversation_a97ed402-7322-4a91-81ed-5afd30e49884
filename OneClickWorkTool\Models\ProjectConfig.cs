using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;

namespace OneClickWorkTool.Models
{
    /// <summary>
    /// 项目配置数据模型
    /// </summary>
    public class ProjectConfig : INotifyPropertyChanged
    {
        private string _name = string.Empty;
        private string _projectPath = string.Empty;
        private string _solutionPath = string.Empty;
        private string _frontendExePath = string.Empty;
        private string _backendExePath = string.Empty;
        private string _dataTableSourcePath = string.Empty;
        private string _buildConfiguration = "Release";
        private string _buildPlatform = "Win32";

        /// <summary>
        /// 项目名称
        /// </summary>
        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        /// <summary>
        /// 项目根路径
        /// </summary>
        public string ProjectPath
        {
            get => _projectPath;
            set => SetProperty(ref _projectPath, value);
        }

        /// <summary>
        /// VS2008解决方案文件路径
        /// </summary>
        public string SolutionPath
        {
            get => _solutionPath;
            set => SetProperty(ref _solutionPath, value);
        }

        /// <summary>
        /// 前端可执行文件路径
        /// </summary>
        public string FrontendExePath
        {
            get => _frontendExePath;
            set => SetProperty(ref _frontendExePath, value);
        }

        /// <summary>
        /// 后端可执行文件路径（兼容性保留）
        /// </summary>
        public string BackendExePath
        {
            get => _backendExePath;
            set => SetProperty(ref _backendExePath, value);
        }

        /// <summary>
        /// 导表工具输出路径
        /// </summary>
        public string DataTableSourcePath
        {
            get => _dataTableSourcePath;
            set => SetProperty(ref _dataTableSourcePath, value);
        }

        /// <summary>
        /// 编译配置（Release/Debug等）
        /// </summary>
        public string BuildConfiguration
        {
            get => _buildConfiguration;
            set => SetProperty(ref _buildConfiguration, value);
        }

        /// <summary>
        /// 编译平台（Win32/x64等）
        /// </summary>
        public string BuildPlatform
        {
            get => _buildPlatform;
            set => SetProperty(ref _buildPlatform, value);
        }

        /// <summary>
        /// 导表文件目标路径列表
        /// </summary>
        public List<string> DataTableTargetPaths { get; set; } = new List<string>();

        /// <summary>
        /// 打包输出路径
        /// </summary>
        public string PackageOutputPath { get; set; } = string.Empty;

        /// <summary>
        /// 前端启动参数
        /// </summary>
        public string FrontendStartupArgs { get; set; } = string.Empty;

        /// <summary>
        /// 后端启动参数
        /// </summary>
        public string BackendStartupArgs { get; set; } = string.Empty;

        /// <summary>
        /// 前端关闭脚本路径
        /// </summary>
        public string FrontendCloseScriptPath { get; set; } = string.Empty;

        /// <summary>
        /// 后端关闭脚本路径（兼容性保留）
        /// </summary>
        public string BackendCloseScriptPath { get; set; } = string.Empty;

        /// <summary>
        /// 服务端关闭可执行文件路径
        /// </summary>
        public string ServerCloseExecutablePath { get; set; } = string.Empty;

        /// <summary>
        /// 服务端关闭可执行文件参数
        /// </summary>
        public string ServerCloseExecutableArgs { get; set; } = string.Empty;

        /// <summary>
        /// 可用的编译配置列表
        /// </summary>
        public List<string> AvailableBuildConfigurations { get; set; } = new List<string> { "Debug", "Release" };

        /// <summary>
        /// 可用的编译平台列表
        /// </summary>
        public List<string> AvailableBuildPlatforms { get; set; } = new List<string> { "Win32", "x64" };

        /// <summary>
        /// 自定义编译配置（用户手动输入）
        /// </summary>
        public List<string> CustomBuildConfigurations { get; set; } = new List<string>();

        /// <summary>
        /// 自定义编译平台（用户手动输入）
        /// </summary>
        public List<string> CustomBuildPlatforms { get; set; } = new List<string>();

        /// <summary>
        /// 是否使用自定义配置
        /// </summary>
        public bool UseCustomConfigurations { get; set; } = false;

        /// <summary>
        /// 后端进程列表（支持多个后端服务）
        /// </summary>
        public List<BackendProcessConfig> BackendProcesses { get; set; } = new List<BackendProcessConfig>();

        /// <summary>
        /// 数据文件配置列表（SData文件管理）
        /// </summary>
        public List<DataFileConfig> DataFileConfigs { get; set; } = new List<DataFileConfig>();

        /// <summary>
        /// 项目唯一标识
        /// </summary>
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreatedTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 最后修改时间
        /// </summary>
        public DateTime LastModifiedTime { get; set; } = DateTime.Now;

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value)) return false;
            field = value;
            LastModifiedTime = DateTime.Now;
            OnPropertyChanged(propertyName);
            return true;
        }

        /// <summary>
        /// 验证项目配置是否有效
        /// </summary>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(Name) &&
                   !string.IsNullOrWhiteSpace(ProjectPath) &&
                   System.IO.Directory.Exists(ProjectPath);
        }

        /// <summary>
        /// 获取项目显示名称
        /// </summary>
        public string DisplayName => string.IsNullOrWhiteSpace(Name) ? "未命名项目" : Name;

        /// <summary>
        /// 获取所有编译配置（自动检测 + 自定义）
        /// </summary>
        public List<string> GetAllBuildConfigurations()
        {
            var allConfigs = new List<string>();

            if (UseCustomConfigurations && CustomBuildConfigurations.Any())
            {
                allConfigs.AddRange(CustomBuildConfigurations);
            }
            else
            {
                allConfigs.AddRange(AvailableBuildConfigurations);
            }

            return allConfigs.Distinct().ToList();
        }

        /// <summary>
        /// 获取所有编译平台（自动检测 + 自定义）
        /// </summary>
        public List<string> GetAllBuildPlatforms()
        {
            var allPlatforms = new List<string>();

            if (UseCustomConfigurations && CustomBuildPlatforms.Any())
            {
                allPlatforms.AddRange(CustomBuildPlatforms);
            }
            else
            {
                allPlatforms.AddRange(AvailableBuildPlatforms);
            }

            return allPlatforms.Distinct().ToList();
        }
    }

    /// <summary>
    /// 后端进程配置
    /// </summary>
    public class BackendProcessConfig
    {
        /// <summary>
        /// 进程名称（如：游戏服、DB服、日志服等）
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 可执行文件路径
        /// </summary>
        public string ExecutablePath { get; set; } = string.Empty;

        /// <summary>
        /// 启动参数
        /// </summary>
        public string StartupArgs { get; set; } = string.Empty;

        /// <summary>
        /// 工作目录
        /// </summary>
        public string WorkingDirectory { get; set; } = string.Empty;

        /// <summary>
        /// 关闭脚本路径
        /// </summary>
        public string CloseScriptPath { get; set; } = string.Empty;

        /// <summary>
        /// 是否启用此进程
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// 启动顺序（数字越小越先启动）
        /// </summary>
        public int StartOrder { get; set; } = 0;

        /// <summary>
        /// 启动延迟（毫秒）
        /// </summary>
        public int StartDelay { get; set; } = 0;

        /// <summary>
        /// 进程唯一标识
        /// </summary>
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// 显示名称
        /// </summary>
        public string DisplayName => string.IsNullOrWhiteSpace(Name) ? "未命名进程" : Name;

        /// <summary>
        /// 验证配置是否有效
        /// </summary>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(Name) &&
                   !string.IsNullOrWhiteSpace(ExecutablePath) &&
                   File.Exists(ExecutablePath);
        }
    }
}
