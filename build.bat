@echo off
chcp 65001 >nul
echo ========================================
echo 一键工作工具 - 构建脚本
echo ========================================
echo.

set PROJECT_DIR=%~dp0OneClickWorkTool
set OUTPUT_DIR=%~dp0bin

if not exist "%PROJECT_DIR%" (
    echo 错误: 找不到项目目录 %PROJECT_DIR%
    pause
    exit /b 1
)

echo 当前目录: %cd%
echo 项目目录: %PROJECT_DIR%
echo 输出目录: %OUTPUT_DIR%
echo.

:menu
echo 请选择操作:
echo 1. 还原依赖包
echo 2. 编译项目 (Debug)
echo 3. 编译项目 (Release)
echo 4. 运行项目
echo 5. 清理输出
echo 6. 发布项目 (Release, 单文件)
echo 7. 退出
echo.
set /p choice=请输入选择 (1-7): 

if "%choice%"=="1" goto restore
if "%choice%"=="2" goto build_debug
if "%choice%"=="3" goto build_release
if "%choice%"=="4" goto run
if "%choice%"=="5" goto clean
if "%choice%"=="6" goto publish
if "%choice%"=="7" goto exit
echo 无效选择，请重新输入
echo.
goto menu

:restore
echo.
echo ========================================
echo 还原依赖包...
echo ========================================
cd /d "%PROJECT_DIR%"
dotnet restore
if %errorlevel% neq 0 (
    echo 还原依赖包失败!
    pause
    goto menu
)
echo 依赖包还原成功!
echo.
pause
goto menu

:build_debug
echo.
echo ========================================
echo 编译项目 (Debug)...
echo ========================================
cd /d "%PROJECT_DIR%"
dotnet build -c Debug
if %errorlevel% neq 0 (
    echo 编译失败!
    pause
    goto menu
)
echo 编译成功!
echo.
pause
goto menu

:build_release
echo.
echo ========================================
echo 编译项目 (Release)...
echo ========================================
cd /d "%PROJECT_DIR%"
dotnet build -c Release
if %errorlevel% neq 0 (
    echo 编译失败!
    pause
    goto menu
)
echo 编译成功!
echo.
pause
goto menu

:run
echo.
echo ========================================
echo 运行项目...
echo ========================================
cd /d "%PROJECT_DIR%"
dotnet run
echo.
pause
goto menu

:clean
echo.
echo ========================================
echo 清理输出...
echo ========================================
cd /d "%PROJECT_DIR%"
dotnet clean
if exist "%OUTPUT_DIR%" (
    echo 删除输出目录: %OUTPUT_DIR%
    rmdir /s /q "%OUTPUT_DIR%"
)
echo 清理完成!
echo.
pause
goto menu

:publish
echo.
echo ========================================
echo 发布项目 (Release, 单文件)...
echo ========================================
if not exist "%OUTPUT_DIR%" mkdir "%OUTPUT_DIR%"
cd /d "%PROJECT_DIR%"
dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true -o "%OUTPUT_DIR%"
if %errorlevel% neq 0 (
    echo 发布失败!
    pause
    goto menu
)
echo.
echo 发布成功!
echo 输出位置: %OUTPUT_DIR%
echo 可执行文件: %OUTPUT_DIR%\OneClickWorkTool.exe
echo.
pause
goto menu

:exit
echo 再见!
exit /b 0
