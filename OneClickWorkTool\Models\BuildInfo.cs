using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace OneClickWorkTool.Models
{
    /// <summary>
    /// 编译信息模型
    /// </summary>
    public class BuildInfo : INotifyPropertyChanged
    {
        private BuildStatus _status = BuildStatus.Ready;
        private string _configuration = "Release";
        private string _platform = "Win32";
        private DateTime _startTime;
        private DateTime _endTime;
        private string _output = string.Empty;
        private int _errorCount;
        private int _warningCount;
        private double _progress;

        /// <summary>
        /// 编译状态
        /// </summary>
        public BuildStatus Status
        {
            get => _status;
            set => SetProperty(ref _status, value);
        }

        /// <summary>
        /// 编译配置
        /// </summary>
        public string Configuration
        {
            get => _configuration;
            set => SetProperty(ref _configuration, value);
        }

        /// <summary>
        /// 编译平台
        /// </summary>
        public string Platform
        {
            get => _platform;
            set => SetProperty(ref _platform, value);
        }

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime StartTime
        {
            get => _startTime;
            set => SetProperty(ref _startTime, value);
        }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime EndTime
        {
            get => _endTime;
            set => SetProperty(ref _endTime, value);
        }

        /// <summary>
        /// 编译输出
        /// </summary>
        public string Output
        {
            get => _output;
            set => SetProperty(ref _output, value);
        }

        /// <summary>
        /// 错误数量
        /// </summary>
        public int ErrorCount
        {
            get => _errorCount;
            set => SetProperty(ref _errorCount, value);
        }

        /// <summary>
        /// 警告数量
        /// </summary>
        public int WarningCount
        {
            get => _warningCount;
            set => SetProperty(ref _warningCount, value);
        }

        /// <summary>
        /// 编译进度（0-100）
        /// </summary>
        public double Progress
        {
            get => _progress;
            set => SetProperty(ref _progress, value);
        }

        /// <summary>
        /// 编译耗时
        /// </summary>
        public TimeSpan Duration
        {
            get
            {
                if (StartTime == default || EndTime == default)
                    return TimeSpan.Zero;
                return EndTime - StartTime;
            }
        }

        /// <summary>
        /// 状态显示文本
        /// </summary>
        public string StatusText
        {
            get
            {
                return Status switch
                {
                    BuildStatus.Ready => "就绪",
                    BuildStatus.Building => "编译中",
                    BuildStatus.Success => "编译成功",
                    BuildStatus.Failed => "编译失败",
                    BuildStatus.Cancelled => "已取消",
                    _ => "未知"
                };
            }
        }

        /// <summary>
        /// 编译结果摘要
        /// </summary>
        public string ResultSummary
        {
            get
            {
                if (Status == BuildStatus.Success)
                {
                    return $"编译成功 - 耗时: {Duration:mm\\:ss}, 警告: {WarningCount}";
                }
                else if (Status == BuildStatus.Failed)
                {
                    return $"编译失败 - 错误: {ErrorCount}, 警告: {WarningCount}";
                }
                else if (Status == BuildStatus.Building)
                {
                    return $"编译中... {Progress:F1}%";
                }
                return StatusText;
            }
        }

        /// <summary>
        /// 是否可以开始编译
        /// </summary>
        public bool CanBuild => Status == BuildStatus.Ready || Status == BuildStatus.Success || Status == BuildStatus.Failed || Status == BuildStatus.Cancelled;

        /// <summary>
        /// 是否正在编译
        /// </summary>
        public bool IsBuilding => Status == BuildStatus.Building;

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);

            // 当状态改变时，通知相关的显示属性
            if (propertyName == nameof(Status))
            {
                OnPropertyChanged(nameof(StatusText));
                OnPropertyChanged(nameof(ResultSummary));
                OnPropertyChanged(nameof(CanBuild));
                OnPropertyChanged(nameof(IsBuilding));
            }
            else if (propertyName == nameof(Progress) || propertyName == nameof(ErrorCount) || propertyName == nameof(WarningCount))
            {
                OnPropertyChanged(nameof(ResultSummary));
            }
            else if (propertyName == nameof(EndTime))
            {
                OnPropertyChanged(nameof(Duration));
                OnPropertyChanged(nameof(ResultSummary));
            }

            return true;
        }

        /// <summary>
        /// 重置编译信息
        /// </summary>
        public void Reset()
        {
            Status = BuildStatus.Ready;
            StartTime = default;
            EndTime = default;
            Output = string.Empty;
            ErrorCount = 0;
            WarningCount = 0;
            Progress = 0;
        }

        /// <summary>
        /// 开始编译
        /// </summary>
        public void StartBuild()
        {
            Status = BuildStatus.Building;
            StartTime = DateTime.Now;
            EndTime = default;
            Output = string.Empty;
            ErrorCount = 0;
            WarningCount = 0;
            Progress = 0;
        }

        /// <summary>
        /// 完成编译
        /// </summary>
        public void CompleteBuild(bool success)
        {
            Status = success ? BuildStatus.Success : BuildStatus.Failed;
            EndTime = DateTime.Now;
            Progress = 100;
        }
    }

    /// <summary>
    /// 编译状态枚举
    /// </summary>
    public enum BuildStatus
    {
        /// <summary>
        /// 就绪状态
        /// </summary>
        Ready,

        /// <summary>
        /// 编译中
        /// </summary>
        Building,

        /// <summary>
        /// 编译成功
        /// </summary>
        Success,

        /// <summary>
        /// 编译失败
        /// </summary>
        Failed,

        /// <summary>
        /// 已取消
        /// </summary>
        Cancelled
    }
}
