using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Management;
using System.Threading;
using System.Threading.Tasks;
using OneClickWorkTool.Models;
using NLog;

namespace OneClickWorkTool.Services
{
    /// <summary>
    /// 进程管理服务
    /// </summary>
    public class ProcessService
    {
        private static readonly Logger Logger = LogManager.GetCurrentClassLogger();
        private readonly Dictionary<ProcessType, ProcessInfo> _processes = new Dictionary<ProcessType, ProcessInfo>();
        private readonly Timer _monitorTimer;
        private readonly object _lockObject = new object();

        public ProcessService()
        {
            // 每秒更新一次进程状态
            _monitorTimer = new Timer(UpdateProcessStatus, null, TimeSpan.FromSeconds(1), TimeSpan.FromSeconds(1));
        }

        /// <summary>
        /// 进程状态改变事件
        /// </summary>
        public event EventHandler<ProcessInfo>? ProcessStatusChanged;

        /// <summary>
        /// 启动进程
        /// </summary>
        public async Task<bool> StartProcessAsync(ProcessType type, string executablePath, string arguments = "", string workingDirectory = "")
        {
            if (string.IsNullOrWhiteSpace(executablePath) || !File.Exists(executablePath))
            {
                Logger.Error($"可执行文件不存在: {executablePath}");
                return false;
            }

            try
            {
                lock (_lockObject)
                {
                    // 如果进程已经在运行，先停止它
                    if (_processes.ContainsKey(type) && _processes[type].Status == ProcessStatus.Running)
                    {
                        Logger.Info($"{type} 进程已在运行，先停止现有进程");
                        StopProcess(type);
                    }

                    // 创建新的进程信息
                    var processInfo = new ProcessInfo
                    {
                        Name = Path.GetFileNameWithoutExtension(executablePath),
                        Type = type,
                        ExecutablePath = executablePath,
                        StartupArgs = arguments,
                        WorkingDirectory = string.IsNullOrWhiteSpace(workingDirectory) ? Path.GetDirectoryName(executablePath) : workingDirectory,
                        Status = ProcessStatus.Starting
                    };

                    _processes[type] = processInfo;
                    OnProcessStatusChanged(processInfo);
                }

                // 启动进程
                var startInfo = new ProcessStartInfo
                {
                    FileName = executablePath,
                    Arguments = arguments,
                    WorkingDirectory = string.IsNullOrWhiteSpace(workingDirectory) ? Path.GetDirectoryName(executablePath) : workingDirectory,
                    UseShellExecute = false,
                    CreateNoWindow = false
                };

                var process = Process.Start(startInfo);
                if (process == null)
                {
                    Logger.Error($"启动 {type} 进程失败");
                    lock (_lockObject)
                    {
                        _processes[type].Status = ProcessStatus.Error;
                        OnProcessStatusChanged(_processes[type]);
                    }
                    return false;
                }

                // 等待进程完全启动
                await Task.Delay(1000);

                lock (_lockObject)
                {
                    _processes[type].Process = process;
                    _processes[type].ProcessId = process.Id;
                    _processes[type].StartTime = DateTime.Now;
                    _processes[type].Status = ProcessStatus.Running;
                    OnProcessStatusChanged(_processes[type]);
                }

                Logger.Info($"{type} 进程启动成功，PID: {process.Id}");
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error(ex, $"启动 {type} 进程时发生错误");
                lock (_lockObject)
                {
                    if (_processes.ContainsKey(type))
                    {
                        _processes[type].Status = ProcessStatus.Error;
                        OnProcessStatusChanged(_processes[type]);
                    }
                }
                return false;
            }
        }

        /// <summary>
        /// 停止进程
        /// </summary>
        public bool StopProcess(ProcessType type)
        {
            lock (_lockObject)
            {
                if (!_processes.ContainsKey(type) || _processes[type].Status != ProcessStatus.Running)
                {
                    Logger.Info($"{type} 进程未运行");
                    return true;
                }

                var processInfo = _processes[type];
                processInfo.Status = ProcessStatus.Stopping;
                OnProcessStatusChanged(processInfo);

                try
                {
                    // 首先尝试使用关闭脚本
                    if (!string.IsNullOrWhiteSpace(processInfo.CloseScriptPath) && File.Exists(processInfo.CloseScriptPath))
                    {
                        Logger.Info($"使用关闭脚本停止 {type} 进程: {processInfo.CloseScriptPath}");
                        var scriptProcess = Process.Start(processInfo.CloseScriptPath);
                        scriptProcess?.WaitForExit(5000); // 等待5秒
                    }

                    // 检查进程是否已经停止
                    if (processInfo.Process != null && !processInfo.Process.HasExited)
                    {
                        // 尝试优雅关闭
                        Logger.Info($"尝试优雅关闭 {type} 进程");
                        processInfo.Process.CloseMainWindow();
                        
                        // 等待3秒
                        if (!processInfo.Process.WaitForExit(3000))
                        {
                            // 强制终止
                            Logger.Info($"强制终止 {type} 进程");
                            processInfo.Process.Kill();
                        }
                    }

                    processInfo.Status = ProcessStatus.Stopped;
                    processInfo.ProcessId = 0;
                    processInfo.Process = null;
                    OnProcessStatusChanged(processInfo);

                    Logger.Info($"{type} 进程已停止");
                    return true;
                }
                catch (Exception ex)
                {
                    Logger.Error(ex, $"停止 {type} 进程时发生错误");
                    processInfo.Status = ProcessStatus.Error;
                    OnProcessStatusChanged(processInfo);
                    return false;
                }
            }
        }

        /// <summary>
        /// 获取进程信息
        /// </summary>
        public ProcessInfo? GetProcessInfo(ProcessType type)
        {
            lock (_lockObject)
            {
                return _processes.ContainsKey(type) ? _processes[type] : null;
            }
        }

        /// <summary>
        /// 获取所有进程信息
        /// </summary>
        public Dictionary<ProcessType, ProcessInfo> GetAllProcesses()
        {
            lock (_lockObject)
            {
                return new Dictionary<ProcessType, ProcessInfo>(_processes);
            }
        }

        /// <summary>
        /// 检查进程是否正在运行
        /// </summary>
        public bool IsProcessRunning(ProcessType type)
        {
            lock (_lockObject)
            {
                return _processes.ContainsKey(type) && _processes[type].Status == ProcessStatus.Running;
            }
        }

        /// <summary>
        /// 获取系统中所有VS2008进程
        /// </summary>
        public List<Process> GetVS2008Processes()
        {
            try
            {
                return Process.GetProcessesByName("devenv").ToList();
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "获取VS2008进程列表时发生错误");
                return new List<Process>();
            }
        }

        /// <summary>
        /// 附加进程到VS2008调试器
        /// </summary>
        public bool AttachToVS2008(int vsProcessId, int targetProcessId)
        {
            try
            {
                // 这里需要使用VS2008的DTE接口来实现进程附加
                // 由于这比较复杂，这里先提供一个基础框架
                Logger.Info($"尝试将进程 {targetProcessId} 附加到 VS2008 进程 {vsProcessId}");
                
                // TODO: 实现VS2008 DTE接口调用
                // 这需要引用Microsoft.VisualStudio.OLE.Interop等COM组件
                
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error(ex, $"附加进程到VS2008时发生错误");
                return false;
            }
        }

        private void UpdateProcessStatus(object? state)
        {
            lock (_lockObject)
            {
                foreach (var kvp in _processes.ToList())
                {
                    var processInfo = kvp.Value;
                    
                    if (processInfo.Process != null && processInfo.Status == ProcessStatus.Running)
                    {
                        try
                        {
                            // 检查进程是否还在运行
                            if (processInfo.Process.HasExited)
                            {
                                processInfo.Status = ProcessStatus.Stopped;
                                processInfo.ProcessId = 0;
                                processInfo.Process = null;
                                OnProcessStatusChanged(processInfo);
                                continue;
                            }

                            // 更新进程信息
                            processInfo.MemoryUsage = processInfo.Process.WorkingSet64;
                            
                            // CPU使用率计算比较复杂，这里简化处理
                            // 实际项目中可以使用PerformanceCounter来获取准确的CPU使用率
                            
                            OnProcessStatusChanged(processInfo);
                        }
                        catch (Exception ex)
                        {
                            Logger.Error(ex, $"更新 {kvp.Key} 进程状态时发生错误");
                            processInfo.Status = ProcessStatus.Error;
                            OnProcessStatusChanged(processInfo);
                        }
                    }
                }
            }
        }

        private void OnProcessStatusChanged(ProcessInfo processInfo)
        {
            ProcessStatusChanged?.Invoke(this, processInfo);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _monitorTimer?.Dispose();
            
            lock (_lockObject)
            {
                foreach (var processInfo in _processes.Values)
                {
                    if (processInfo.Status == ProcessStatus.Running)
                    {
                        StopProcess(processInfo.Type);
                    }
                }
            }
        }
    }
}
