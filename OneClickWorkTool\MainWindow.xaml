<Window x:Class="OneClickWorkTool.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="一键工作工具" Height="700" Width="1000"
        WindowStartupLocation="CenterScreen">

    <Window.Resources>
        <!-- 按钮样式 -->
        <Style x:Key="PrimaryButton" TargetType="Button">
            <Setter Property="Background" Value="#007ACC"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="12,6"/>
            <Setter Property="Margin" Value="4"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#005A9E"/>
                </Trigger>
                <Trigger Property="IsEnabled" Value="False">
                    <Setter Property="Background" Value="#CCCCCC"/>
                    <Setter Property="Foreground" Value="#666666"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <Style x:Key="SecondaryButton" TargetType="Button">
            <Setter Property="Background" Value="#F0F0F0"/>
            <Setter Property="Foreground" Value="#333333"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#CCCCCC"/>
            <Setter Property="Padding" Value="12,6"/>
            <Setter Property="Margin" Value="4"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#E0E0E0"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- 状态指示器样式 -->
        <Style x:Key="StatusIndicator" TargetType="Ellipse">
            <Setter Property="Width" Value="12"/>
            <Setter Property="Height" Value="12"/>
            <Setter Property="Margin" Value="4"/>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 项目切换区域 -->
        <Border Grid.Row="0" Background="#F8F8F8" BorderBrush="#E0E0E0" BorderThickness="0,0,0,1" Padding="10">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Name="PrevProjectButton" Content="◀" Style="{StaticResource SecondaryButton}" Click="PrevProjectButton_Click"/>
                <ComboBox Name="ProjectComboBox" Width="300" Margin="10,0"
                          SelectionChanged="ProjectComboBox_SelectionChanged"
                          DisplayMemberPath="DisplayName"/>
                <Button Name="NextProjectButton" Content="▶" Style="{StaticResource SecondaryButton}" Click="NextProjectButton_Click"/>
                <Button Name="AddProjectButton" Content="添加项目" Style="{StaticResource PrimaryButton}" Click="AddProjectButton_Click"/>
                <Button Name="SettingsButton" Content="设置" Style="{StaticResource SecondaryButton}" Click="SettingsButton_Click"/>
                <Button Name="AdvancedConfigButton" Content="高级配置" Style="{StaticResource SecondaryButton}" Click="AdvancedConfigButton_Click"/>
            </StackPanel>
        </Border>

        <!-- 编译区域 -->
        <Border Grid.Row="1" Background="White" BorderBrush="#E0E0E0" BorderThickness="0,0,0,1" Padding="10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <Label Grid.Column="0" Content="编译配置:" VerticalAlignment="Center"/>
                <ComboBox Grid.Column="1" Name="BuildConfigComboBox" Width="100" Margin="5,0"/>
                <ComboBox Grid.Column="2" Name="BuildPlatformComboBox" Width="80" Margin="5,0"/>

                <StackPanel Grid.Column="3" Orientation="Horizontal" Margin="10,0">
                    <Button Name="BuildButton" Content="编译" Style="{StaticResource PrimaryButton}" Click="BuildButton_Click"/>
                    <Button Name="CancelBuildButton" Content="取消" Style="{StaticResource SecondaryButton}" Click="CancelBuildButton_Click" IsEnabled="False"/>
                </StackPanel>

                <StackPanel Grid.Column="4" Orientation="Horizontal" VerticalAlignment="Center">
                    <Ellipse Name="BuildStatusIndicator" Style="{StaticResource StatusIndicator}" Fill="Gray"/>
                    <TextBlock Name="BuildStatusText" Text="就绪" VerticalAlignment="Center" Margin="5,0"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 进程管理区域 -->
        <Border Grid.Row="2" Background="White" BorderBrush="#E0E0E0" BorderThickness="0,0,0,1" Padding="10">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- 进程控制按钮 -->
                <StackPanel Grid.Row="0" Orientation="Horizontal">
                    <Button Name="StartFrontendButton" Content="启动前端" Style="{StaticResource PrimaryButton}" Click="StartFrontendButton_Click"/>
                    <Button Name="StartBackendButton" Content="启动后端" Style="{StaticResource PrimaryButton}" Click="StartBackendButton_Click"/>
                    <Button Name="StartAllButton" Content="启动全部" Style="{StaticResource PrimaryButton}" Click="StartAllButton_Click"/>
                    <Button Name="StopFrontendButton" Content="停止前端" Style="{StaticResource SecondaryButton}" Click="StopFrontendButton_Click"/>
                    <Button Name="StopBackendButton" Content="停止后端" Style="{StaticResource SecondaryButton}" Click="StopBackendButton_Click"/>
                    <Button Name="StopAllButton" Content="停止全部" Style="{StaticResource SecondaryButton}" Click="StopAllButton_Click"/>
                    <Button Name="AttachToVSButton" Content="附加到VS" Style="{StaticResource SecondaryButton}" Click="AttachToVSButton_Click"/>
                </StackPanel>

                <!-- 进程状态显示 -->
                <Grid Grid.Row="1" Margin="0,10,0,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="300"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- 前端进程状态 -->
                    <Border Grid.Column="0" BorderBrush="#E0E0E0" BorderThickness="1" Margin="0,0,10,0" Padding="10">
                        <StackPanel>
                            <TextBlock Text="前端进程" FontWeight="Bold" Margin="0,0,0,5"/>
                            <StackPanel Orientation="Horizontal">
                                <Ellipse Name="FrontendStatusIndicator" Style="{StaticResource StatusIndicator}" Fill="Gray"/>
                                <TextBlock Name="FrontendStatusText" Text="未运行" VerticalAlignment="Center"/>
                            </StackPanel>
                            <TextBlock Name="FrontendProcessIdText" Text="PID: -" Margin="0,2"/>
                            <TextBlock Name="FrontendMemoryText" Text="内存: -" Margin="0,2"/>
                            <TextBlock Name="FrontendRuntimeText" Text="运行时间: -" Margin="0,2"/>
                        </StackPanel>
                    </Border>

                    <!-- 后端进程状态列表 -->
                    <Border Grid.Column="1" BorderBrush="#E0E0E0" BorderThickness="1" Padding="10">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,5">
                                <TextBlock Text="后端进程" FontWeight="Bold" VerticalAlignment="Center"/>
                                <TextBlock Name="BackendProcessCountText" Text="(0个)" Margin="5,0" VerticalAlignment="Center" Foreground="Gray"/>
                            </StackPanel>

                            <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" MaxHeight="120">
                                <ItemsControl Name="BackendProcessList">
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <Border BorderBrush="#F0F0F0" BorderThickness="0,0,0,1" Padding="5,3">
                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="15"/>
                                                        <ColumnDefinition Width="80"/>
                                                        <ColumnDefinition Width="60"/>
                                                        <ColumnDefinition Width="80"/>
                                                        <ColumnDefinition Width="*"/>
                                                    </Grid.ColumnDefinitions>

                                                    <Ellipse Grid.Column="0" Width="10" Height="10" Fill="{Binding StatusColor}" VerticalAlignment="Center"/>
                                                    <TextBlock Grid.Column="1" Text="{Binding Name}" FontWeight="SemiBold" VerticalAlignment="Center" FontSize="11"/>
                                                    <TextBlock Grid.Column="2" Text="{Binding StatusText}" VerticalAlignment="Center" FontSize="10"/>
                                                    <TextBlock Grid.Column="3" Text="{Binding PidText}" VerticalAlignment="Center" FontSize="10"/>
                                                    <TextBlock Grid.Column="4" Text="{Binding MemoryText}" VerticalAlignment="Center" FontSize="10"/>
                                                </Grid>
                                            </Border>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>
                            </ScrollViewer>
                        </Grid>
                    </Border>
                </Grid>
            </Grid>
        </Border>

        <!-- 文件管理区域 -->
        <Border Grid.Row="3" Background="White" BorderBrush="#E0E0E0" BorderThickness="0,0,0,1" Padding="10">
            <StackPanel Orientation="Horizontal">
                <Button Name="CopyDataTablesButton" Content="复制导表文件" Style="{StaticResource PrimaryButton}" Click="CopyDataTablesButton_Click"/>
                <Button Name="PackageFilesButton" Content="打包文件" Style="{StaticResource PrimaryButton}" Click="PackageFilesButton_Click"/>
                <Button Name="OpenProjectFolderButton" Content="打开项目文件夹" Style="{StaticResource SecondaryButton}" Click="OpenProjectFolderButton_Click"/>
                <Button Name="OpenOutputFolderButton" Content="打开输出文件夹" Style="{StaticResource SecondaryButton}" Click="OpenOutputFolderButton_Click"/>
            </StackPanel>
        </Border>

        <!-- 日志输出区域 -->
        <Border Grid.Row="4" Background="White" Padding="10">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,5">
                    <TextBlock Text="日志输出" FontWeight="Bold" VerticalAlignment="Center"/>
                    <Button Name="ClearLogButton" Content="清空" Style="{StaticResource SecondaryButton}"
                            Margin="10,0,0,0" Padding="8,4" Click="ClearLogButton_Click"/>
                </StackPanel>

                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Auto">
                    <TextBox Name="LogTextBox"
                             IsReadOnly="True"
                             Background="Black"
                             Foreground="White"
                             FontFamily="Consolas"
                             FontSize="11"
                             TextWrapping="Wrap"
                             VerticalScrollBarVisibility="Auto"
                             HorizontalScrollBarVisibility="Auto"/>
                </ScrollViewer>
            </Grid>
        </Border>
    </Grid>
</Window>
