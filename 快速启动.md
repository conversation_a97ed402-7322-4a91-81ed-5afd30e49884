# 🚀 快速启动指南

## 立即开始（3步完成）

### 第1步：打开VS Code
1. 启动 VS Code
2. 选择 `文件` → `打开文件夹`
3. 选择 `d:\一键工作` 文件夹

### 第2步：运行PowerShell脚本
1. 在VS Code中按 `Ctrl+Shift+` ` 打开终端
2. 输入命令：
   ```powershell
   .\run.ps1
   ```
3. 按回车执行

### 第3步：等待启动
- 脚本会自动：还原依赖 → 编译项目 → 启动应用
- 看到"一键工作工具"窗口表示成功！

---

## 如果遇到PowerShell执行策略错误

在终端中先运行：
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```
然后再运行 `.\run.ps1`

---

## 备用方法（手动命令）

如果PowerShell脚本不工作，在VS Code终端中依次运行：

```bash
cd OneClickWorkTool
dotnet restore
dotnet build
dotnet run
```

---

## 成功标志

✅ 看到"一键工作工具"主窗口  
✅ 界面包含：项目切换、编译、进程管理、文件管理、日志区域  
✅ 可以点击"添加项目"按钮  

---

## 下一步

1. 点击"添加项目"配置您的游戏项目
2. 设置项目路径和VS2008解决方案文件
3. 配置前后端可执行文件
4. 开始使用编译和进程管理功能

---

**需要帮助？** 查看 `VS Code使用指南.md` 获取详细说明
