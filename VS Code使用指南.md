# VS Code 使用指南 - 一键工作工具

## 前提条件确认

✅ 您已经安装了：
- .NET 9.0 SDK
- VS Code
- C# 扩展
- .NET Install Tool 扩展

## 第一步：在VS Code中打开项目

1. 打开 VS Code
2. 选择 `文件` → `打开文件夹`
3. 选择 `d:\一键工作` 文件夹
4. 点击 `选择文件夹`

## 第二步：验证扩展安装

在VS Code中按 `Ctrl+Shift+X` 打开扩展面板，确认已安装：

1. **C# for Visual Studio Code** (ms-dotnettools.csharp)
2. **.NET Install Tool for Extension Authors** (ms-dotnettools.vscode-dotnet-runtime)

如果没有安装，请搜索并安装这两个扩展。

## 第三步：运行项目的三种方法

### 方法一：使用PowerShell脚本（推荐）

1. 在VS Code中按 `Ctrl+Shift+` ` 打开终端
2. 在终端中输入：
   ```powershell
   .\run.ps1
   ```
3. 按回车执行

如果提示执行策略错误，先运行：
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### 方法二：使用VS Code任务

1. 按 `Ctrl+Shift+P` 打开命令面板
2. 输入 `Tasks: Run Task`
3. 选择 `restore` 还原依赖包
4. 完成后再选择 `build` 编译项目
5. 最后按 `F5` 运行项目

### 方法三：使用命令行

在VS Code终端中依次执行：
```bash
cd OneClickWorkTool
dotnet restore
dotnet build
dotnet run
```

## 第四步：调试项目

1. 在代码中设置断点（点击行号左侧）
2. 按 `F5` 开始调试
3. 或者按 `Ctrl+F5` 运行而不调试

## 常见问题解决

### 问题1：提示找不到.NET SDK
**解决方案**：
1. 重启VS Code
2. 在终端中运行 `dotnet --version` 确认安装
3. 如果还是不行，重启电脑

### 问题2：C#扩展无法加载
**解决方案**：
1. 按 `Ctrl+Shift+P`
2. 输入 `Developer: Reload Window`
3. 等待扩展重新加载

### 问题3：编译错误
**解决方案**：
1. 确保在正确的目录（OneClickWorkTool文件夹）
2. 运行 `dotnet clean` 清理项目
3. 再运行 `dotnet restore` 和 `dotnet build`

### 问题4：PowerShell执行策略错误
**解决方案**：
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### 问题5：缺少Windows Forms引用
**解决方案**：
项目已经配置了Windows Forms引用，如果还有问题，在项目目录运行：
```bash
dotnet add package Microsoft.WindowsDesktop.App
```

## VS Code 快捷键

- `F5`: 开始调试
- `Ctrl+F5`: 运行而不调试
- `Ctrl+Shift+P`: 命令面板
- `Ctrl+Shift+` `: 打开终端
- `Ctrl+Shift+E`: 文件资源管理器
- `Ctrl+Shift+X`: 扩展面板
- `Ctrl+B`: 切换侧边栏

## 项目结构说明

```
一键工作/
├── .vscode/                    # VS Code 配置文件
│   ├── tasks.json             # 构建任务配置
│   ├── launch.json            # 调试配置
│   └── settings.json          # 项目设置
├── OneClickWorkTool/          # 主项目
│   ├── Models/                # 数据模型
│   ├── Services/              # 业务服务
│   ├── Views/                 # 界面视图
│   └── OneClickWorkTool.csproj # 项目文件
├── run.ps1                    # PowerShell启动脚本
└── VS Code使用指南.md         # 本文档
```

## 成功运行的标志

当您成功运行项目时，应该看到：

1. **终端输出**：显示编译成功信息
2. **应用程序窗口**：弹出"一键工作工具"主窗口
3. **界面元素**：
   - 顶部项目切换区域
   - 编译配置区域
   - 进程管理区域
   - 文件管理区域
   - 底部日志输出区域

## 下一步

项目成功运行后，您可以：

1. 点击"添加项目"配置您的第一个游戏项目
2. 设置VS2008路径
3. 配置前后端可执行文件路径
4. 开始使用编译和进程管理功能

## 获取帮助

如果遇到问题：

1. 查看VS Code的"问题"面板（`Ctrl+Shift+M`）
2. 查看终端输出的错误信息
3. 检查项目的日志文件
4. 参考项目的README.md文档

---

**提示**：首次运行可能需要下载一些依赖包，请耐心等待。确保网络连接正常。
