@echo off
chcp 65001 >nul
echo ========================================
echo OneClickWorkTool - Startup Script
echo ========================================
echo.

echo Checking .NET version...
dotnet --version
if %errorlevel% neq 0 (
    echo Error: .NET SDK not found
    pause
    exit /b 1
)

echo.
echo Checking project file...
if not exist "OneClickWorkTool\OneClickWorkTool.csproj" (
    echo Error: Project file not found
    pause
    exit /b 1
)

echo Success - Project file exists
echo.

echo Restoring dependencies...
cd OneClickWorkTool
dotnet restore
if %errorlevel% neq 0 (
    echo Error: Failed to restore dependencies
    cd ..
    pause
    exit /b 1
)

echo.
echo Building project...
dotnet build -c Release
if %errorlevel% neq 0 (
    echo Error: Build failed
    cd ..
    pause
    exit /b 1
)

echo.
echo Starting application...
echo ========================================
echo.

dotnet run

cd ..
echo.
echo Application exited
pause
