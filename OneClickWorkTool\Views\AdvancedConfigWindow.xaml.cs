using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using OneClickWorkTool.Models;
using OneClickWorkTool.Services;

namespace OneClickWorkTool.Views
{
    /// <summary>
    /// AdvancedConfigWindow.xaml 的交互逻辑
    /// </summary>
    public partial class AdvancedConfigWindow : Window
    {
        public ProjectConfig ProjectConfig { get; private set; }

        private readonly BuildService _buildService;
        private readonly ObservableCollection<string> _customConfigs = new ObservableCollection<string>();
        private readonly ObservableCollection<string> _customPlatforms = new ObservableCollection<string>();
        private readonly ObservableCollection<BackendProcessConfig> _backendProcesses = new ObservableCollection<BackendProcessConfig>();

        public AdvancedConfigWindow(ProjectConfig projectConfig, BuildService buildService)
        {
            InitializeComponent();

            ProjectConfig = projectConfig ?? throw new ArgumentNullException(nameof(projectConfig));
            _buildService = buildService ?? throw new ArgumentNullException(nameof(buildService));

            InitializeUI();
            LoadProjectConfig();
        }

        private void InitializeUI()
        {
            // 绑定数据源
            CustomConfigListBox.ItemsSource = _customConfigs;
            CustomPlatformListBox.ItemsSource = _customPlatforms;
            BackendProcessDataGrid.ItemsSource = _backendProcesses;
        }

        private async void LoadProjectConfig()
        {
            // 加载自定义配置
            UseCustomConfigCheckBox.IsChecked = ProjectConfig.UseCustomConfigurations;

            _customConfigs.Clear();
            foreach (var config in ProjectConfig.CustomBuildConfigurations)
            {
                _customConfigs.Add(config);
            }

            _customPlatforms.Clear();
            foreach (var platform in ProjectConfig.CustomBuildPlatforms)
            {
                _customPlatforms.Add(platform);
            }

            // 加载后端进程
            _backendProcesses.Clear();
            foreach (var process in ProjectConfig.BackendProcesses.OrderBy(p => p.StartOrder))
            {
                _backendProcesses.Add(process);
            }

            // 更新UI状态
            UpdateCustomConfigUI();

            // 加载自动检测的配置
            await LoadDetectedConfigurations();
        }

        private async Task LoadDetectedConfigurations()
        {
            if (string.IsNullOrWhiteSpace(ProjectConfig.SolutionPath))
            {
                DetectedConfigListBox.ItemsSource = new[] { "未配置解决方案文件" };
                DetectedPlatformListBox.ItemsSource = new[] { "未配置解决方案文件" };
                return;
            }

            try
            {
                var (configs, platforms) = await _buildService.DetectBuildConfigurationsAsync(ProjectConfig.SolutionPath);
                DetectedConfigListBox.ItemsSource = configs;
                DetectedPlatformListBox.ItemsSource = platforms;
            }
            catch (Exception ex)
            {
                DetectedConfigListBox.ItemsSource = new[] { $"检测失败: {ex.Message}" };
                DetectedPlatformListBox.ItemsSource = new[] { $"检测失败: {ex.Message}" };
            }
        }

        private void UpdateCustomConfigUI()
        {
            var useCustom = UseCustomConfigCheckBox.IsChecked == true;
            CustomConfigGrid.IsEnabled = useCustom;
        }

        #region 编译配置事件

        private void UseCustomConfigCheckBox_Checked(object sender, RoutedEventArgs e)
        {
            UpdateCustomConfigUI();
        }

        private void UseCustomConfigCheckBox_Unchecked(object sender, RoutedEventArgs e)
        {
            UpdateCustomConfigUI();
        }

        private void AddConfigButton_Click(object sender, RoutedEventArgs e)
        {
            var config = NewConfigTextBox.Text.Trim();
            if (!string.IsNullOrEmpty(config) && !_customConfigs.Contains(config))
            {
                _customConfigs.Add(config);
                NewConfigTextBox.Clear();
            }
        }

        private void RemoveConfigButton_Click(object sender, RoutedEventArgs e)
        {
            if (CustomConfigListBox.SelectedItem is string selectedConfig)
            {
                _customConfigs.Remove(selectedConfig);
            }
        }

        private void AddPlatformButton_Click(object sender, RoutedEventArgs e)
        {
            var platform = NewPlatformTextBox.Text.Trim();
            if (!string.IsNullOrEmpty(platform) && !_customPlatforms.Contains(platform))
            {
                _customPlatforms.Add(platform);
                NewPlatformTextBox.Clear();
            }
        }

        private void RemovePlatformButton_Click(object sender, RoutedEventArgs e)
        {
            if (CustomPlatformListBox.SelectedItem is string selectedPlatform)
            {
                _customPlatforms.Remove(selectedPlatform);
            }
        }

        private void NewConfigTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                AddConfigButton_Click(sender, e);
            }
        }

        private void NewPlatformTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                AddPlatformButton_Click(sender, e);
            }
        }

        private async void RefreshConfigButton_Click(object sender, RoutedEventArgs e)
        {
            await LoadDetectedConfigurations();
        }

        #endregion

        #region 后端进程事件

        private void AddProcessButton_Click(object sender, RoutedEventArgs e)
        {
            // 暂时使用简单的输入对话框，后续可以创建专门的配置窗口
            var name = Microsoft.VisualBasic.Interaction.InputBox("请输入进程名称:", "添加后端进程", "");
            if (string.IsNullOrWhiteSpace(name)) return;

            var execPath = Microsoft.VisualBasic.Interaction.InputBox("请输入可执行文件路径:", "添加后端进程", "");
            if (string.IsNullOrWhiteSpace(execPath)) return;

            var process = new BackendProcessConfig
            {
                Name = name,
                ExecutablePath = execPath,
                StartOrder = _backendProcesses.Count,
                IsEnabled = true
            };

            _backendProcesses.Add(process);
        }

        private void EditProcessButton_Click(object sender, RoutedEventArgs e)
        {
            if (BackendProcessDataGrid.SelectedItem is BackendProcessConfig selectedProcess)
            {
                var name = Microsoft.VisualBasic.Interaction.InputBox("请输入进程名称:", "编辑后端进程", selectedProcess.Name);
                if (string.IsNullOrWhiteSpace(name)) return;

                var execPath = Microsoft.VisualBasic.Interaction.InputBox("请输入可执行文件路径:", "编辑后端进程", selectedProcess.ExecutablePath);
                if (string.IsNullOrWhiteSpace(execPath)) return;

                selectedProcess.Name = name;
                selectedProcess.ExecutablePath = execPath;

                // 刷新DataGrid显示
                BackendProcessDataGrid.Items.Refresh();
            }
        }

        private void RemoveProcessButton_Click(object sender, RoutedEventArgs e)
        {
            if (BackendProcessDataGrid.SelectedItem is BackendProcessConfig selectedProcess)
            {
                _backendProcesses.Remove(selectedProcess);

                // 重新排序
                for (int i = 0; i < _backendProcesses.Count; i++)
                {
                    _backendProcesses[i].StartOrder = i;
                }
            }
        }

        private void MoveUpButton_Click(object sender, RoutedEventArgs e)
        {
            if (BackendProcessDataGrid.SelectedItem is BackendProcessConfig selectedProcess)
            {
                var index = _backendProcesses.IndexOf(selectedProcess);
                if (index > 0)
                {
                    _backendProcesses.Move(index, index - 1);

                    // 更新启动顺序
                    for (int i = 0; i < _backendProcesses.Count; i++)
                    {
                        _backendProcesses[i].StartOrder = i;
                    }
                }
            }
        }

        private void MoveDownButton_Click(object sender, RoutedEventArgs e)
        {
            if (BackendProcessDataGrid.SelectedItem is BackendProcessConfig selectedProcess)
            {
                var index = _backendProcesses.IndexOf(selectedProcess);
                if (index < _backendProcesses.Count - 1)
                {
                    _backendProcesses.Move(index, index + 1);

                    // 更新启动顺序
                    for (int i = 0; i < _backendProcesses.Count; i++)
                    {
                        _backendProcesses[i].StartOrder = i;
                    }
                }
            }
        }

        #endregion

        #region 按钮事件

        private void OkButton_Click(object sender, RoutedEventArgs e)
        {
            SaveConfiguration();
            DialogResult = true;
            Close();
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private void SaveConfiguration()
        {
            // 保存编译配置
            ProjectConfig.UseCustomConfigurations = UseCustomConfigCheckBox.IsChecked == true;

            ProjectConfig.CustomBuildConfigurations.Clear();
            ProjectConfig.CustomBuildConfigurations.AddRange(_customConfigs);

            ProjectConfig.CustomBuildPlatforms.Clear();
            ProjectConfig.CustomBuildPlatforms.AddRange(_customPlatforms);

            // 保存后端进程配置
            ProjectConfig.BackendProcesses.Clear();
            ProjectConfig.BackendProcesses.AddRange(_backendProcesses);
        }

        #endregion
    }
}
