<Window x:Class="OneClickWorkTool.Views.AdvancedConfigWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="高级配置" Height="700" Width="900"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize">
    
    <Window.Resources>
        <Style x:Key="SectionHeader" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Margin" Value="0,15,0,10"/>
            <Setter Property="Foreground" Value="#333333"/>
        </Style>
        
        <Style x:Key="SubHeader" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Margin" Value="0,10,0,5"/>
            <Setter Property="Foreground" Value="#555555"/>
        </Style>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <TabControl Grid.Row="0">
            <!-- 编译配置标签页 -->
            <TabItem Header="编译配置">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="10">
                        <TextBlock Text="编译配置管理" Style="{StaticResource SectionHeader}"/>
                        
                        <CheckBox Name="UseCustomConfigCheckBox" Content="使用自定义编译配置" 
                                  Margin="0,5" Checked="UseCustomConfigCheckBox_Checked" 
                                  Unchecked="UseCustomConfigCheckBox_Unchecked"/>
                        
                        <TextBlock Text="说明：勾选后将使用下方自定义的配置，否则使用从解决方案文件自动检测的配置" 
                                   Foreground="Gray" FontSize="10" Margin="20,0,0,10"/>

                        <!-- 自定义编译配置 -->
                        <Grid Name="CustomConfigGrid" IsEnabled="False">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="20"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- 编译配置 -->
                            <StackPanel Grid.Column="0">
                                <TextBlock Text="自定义编译配置" Style="{StaticResource SubHeader}"/>
                                <ListBox Name="CustomConfigListBox" Height="150" Margin="0,5"/>
                                <StackPanel Orientation="Horizontal" Margin="0,5">
                                    <TextBox Name="NewConfigTextBox" Width="150" Margin="0,0,5,0" 
                                             KeyDown="NewConfigTextBox_KeyDown"/>
                                    <Button Name="AddConfigButton" Content="添加" Padding="10,5" 
                                            Click="AddConfigButton_Click"/>
                                    <Button Name="RemoveConfigButton" Content="删除" Padding="10,5" 
                                            Margin="5,0,0,0" Click="RemoveConfigButton_Click"/>
                                </StackPanel>
                            </StackPanel>

                            <!-- 编译平台 -->
                            <StackPanel Grid.Column="2">
                                <TextBlock Text="自定义编译平台" Style="{StaticResource SubHeader}"/>
                                <ListBox Name="CustomPlatformListBox" Height="150" Margin="0,5"/>
                                <StackPanel Orientation="Horizontal" Margin="0,5">
                                    <TextBox Name="NewPlatformTextBox" Width="150" Margin="0,0,5,0" 
                                             KeyDown="NewPlatformTextBox_KeyDown"/>
                                    <Button Name="AddPlatformButton" Content="添加" Padding="10,5" 
                                            Click="AddPlatformButton_Click"/>
                                    <Button Name="RemovePlatformButton" Content="删除" Padding="10,5" 
                                            Margin="5,0,0,0" Click="RemovePlatformButton_Click"/>
                                </StackPanel>
                            </StackPanel>
                        </Grid>

                        <!-- 自动检测的配置显示 -->
                        <StackPanel Name="AutoConfigPanel">
                            <TextBlock Text="自动检测的配置" Style="{StaticResource SubHeader}"/>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="20"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <StackPanel Grid.Column="0">
                                    <TextBlock Text="检测到的编译配置:" FontWeight="SemiBold" Margin="0,5"/>
                                    <ListBox Name="DetectedConfigListBox" Height="100" IsEnabled="False"/>
                                </StackPanel>
                                
                                <StackPanel Grid.Column="2">
                                    <TextBlock Text="检测到的编译平台:" FontWeight="SemiBold" Margin="0,5"/>
                                    <ListBox Name="DetectedPlatformListBox" Height="100" IsEnabled="False"/>
                                </StackPanel>
                            </Grid>
                            
                            <Button Name="RefreshConfigButton" Content="重新检测配置" 
                                    HorizontalAlignment="Left" Margin="0,10" Padding="15,8"
                                    Click="RefreshConfigButton_Click"/>
                        </StackPanel>
                    </StackPanel>
                </ScrollViewer>
            </TabItem>

            <!-- 后端进程标签页 -->
            <TabItem Header="后端进程">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <StackPanel Grid.Row="0">
                        <TextBlock Text="后端进程管理" Style="{StaticResource SectionHeader}"/>
                        <TextBlock Text="配置多个后端服务进程（如：游戏服、DB服、日志服、登录服等）" 
                                   Foreground="Gray" FontSize="11" Margin="0,0,0,10"/>
                    </StackPanel>

                    <DataGrid Grid.Row="1" Name="BackendProcessDataGrid" 
                              AutoGenerateColumns="False" CanUserAddRows="False"
                              GridLinesVisibility="Horizontal" HeadersVisibility="Column">
                        <DataGrid.Columns>
                            <DataGridCheckBoxColumn Header="启用" Binding="{Binding IsEnabled}" Width="50"/>
                            <DataGridTextColumn Header="进程名称" Binding="{Binding Name}" Width="100"/>
                            <DataGridTextColumn Header="可执行文件" Binding="{Binding ExecutablePath}" Width="200"/>
                            <DataGridTextColumn Header="启动参数" Binding="{Binding StartupArgs}" Width="150"/>
                            <DataGridTextColumn Header="启动顺序" Binding="{Binding StartOrder}" Width="80"/>
                            <DataGridTextColumn Header="延迟(ms)" Binding="{Binding StartDelay}" Width="80"/>
                        </DataGrid.Columns>
                    </DataGrid>

                    <StackPanel Grid.Row="2" Orientation="Horizontal" Margin="0,10,0,0">
                        <Button Name="AddProcessButton" Content="添加进程" Padding="15,8" 
                                Click="AddProcessButton_Click"/>
                        <Button Name="EditProcessButton" Content="编辑进程" Padding="15,8" 
                                Margin="10,0,0,0" Click="EditProcessButton_Click"/>
                        <Button Name="RemoveProcessButton" Content="删除进程" Padding="15,8" 
                                Margin="10,0,0,0" Click="RemoveProcessButton_Click"/>
                        <Button Name="MoveUpButton" Content="上移" Padding="15,8" 
                                Margin="20,0,0,0" Click="MoveUpButton_Click"/>
                        <Button Name="MoveDownButton" Content="下移" Padding="15,8" 
                                Margin="10,0,0,0" Click="MoveDownButton_Click"/>
                    </StackPanel>
                </Grid>
            </TabItem>
        </TabControl>

        <!-- 按钮区域 -->
        <StackPanel Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,20,0,0">
            <Button Name="OkButton" Content="确定" Width="80" Height="30" Margin="5" 
                    Click="OkButton_Click" IsDefault="True"/>
            <Button Name="CancelButton" Content="取消" Width="80" Height="30" Margin="5" 
                    Click="CancelButton_Click" IsCancel="True"/>
        </StackPanel>
    </Grid>
</Window>
