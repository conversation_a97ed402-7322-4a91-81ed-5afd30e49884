using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Newtonsoft.Json;
using OneClickWorkTool.Models;
using NLog;

namespace OneClickWorkTool.Services
{
    /// <summary>
    /// 配置管理服务
    /// </summary>
    public class ConfigurationService
    {
        private static readonly Logger Logger = LogManager.GetCurrentClassLogger();
        private const string ConfigFileName = "projects.json";
        private const string AppConfigFileName = "app.config.json";
        
        private readonly string _configDirectory;
        private readonly string _projectConfigPath;
        private readonly string _appConfigPath;
        
        private List<ProjectConfig> _projects = new List<ProjectConfig>();
        private AppConfig _appConfig = new AppConfig();

        public ConfigurationService()
        {
            // 配置文件存储在用户文档目录下的应用程序文件夹中
            _configDirectory = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments),
                "OneClickWorkTool");
            
            _projectConfigPath = Path.Combine(_configDirectory, ConfigFileName);
            _appConfigPath = Path.Combine(_configDirectory, AppConfigFileName);
            
            EnsureConfigDirectoryExists();
        }

        /// <summary>
        /// 获取所有项目配置
        /// </summary>
        public List<ProjectConfig> GetAllProjects()
        {
            return new List<ProjectConfig>(_projects);
        }

        /// <summary>
        /// 获取应用程序配置
        /// </summary>
        public AppConfig GetAppConfig()
        {
            return _appConfig;
        }

        /// <summary>
        /// 加载配置
        /// </summary>
        public void LoadConfigurations()
        {
            try
            {
                LoadProjectConfigurations();
                LoadAppConfiguration();
                Logger.Info("配置加载成功");
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "加载配置时发生错误");
                throw;
            }
        }

        /// <summary>
        /// 保存项目配置
        /// </summary>
        public void SaveProjectConfigurations()
        {
            try
            {
                var json = JsonConvert.SerializeObject(_projects, Formatting.Indented);
                File.WriteAllText(_projectConfigPath, json);
                Logger.Info($"项目配置已保存到: {_projectConfigPath}");
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "保存项目配置时发生错误");
                throw;
            }
        }

        /// <summary>
        /// 保存应用程序配置
        /// </summary>
        public void SaveAppConfiguration()
        {
            try
            {
                var json = JsonConvert.SerializeObject(_appConfig, Formatting.Indented);
                File.WriteAllText(_appConfigPath, json);
                Logger.Info($"应用程序配置已保存到: {_appConfigPath}");
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "保存应用程序配置时发生错误");
                throw;
            }
        }

        /// <summary>
        /// 添加项目配置
        /// </summary>
        public void AddProject(ProjectConfig project)
        {
            if (project == null)
                throw new ArgumentNullException(nameof(project));

            // 检查是否已存在同名项目
            if (_projects.Any(p => p.Name.Equals(project.Name, StringComparison.OrdinalIgnoreCase)))
            {
                throw new InvalidOperationException($"项目名称 '{project.Name}' 已存在");
            }

            _projects.Add(project);
            SaveProjectConfigurations();
            Logger.Info($"添加项目: {project.Name}");
        }

        /// <summary>
        /// 更新项目配置
        /// </summary>
        public void UpdateProject(ProjectConfig project)
        {
            if (project == null)
                throw new ArgumentNullException(nameof(project));

            var existingProject = _projects.FirstOrDefault(p => p.Id == project.Id);
            if (existingProject == null)
            {
                throw new InvalidOperationException($"未找到ID为 '{project.Id}' 的项目");
            }

            var index = _projects.IndexOf(existingProject);
            _projects[index] = project;
            SaveProjectConfigurations();
            Logger.Info($"更新项目: {project.Name}");
        }

        /// <summary>
        /// 删除项目配置
        /// </summary>
        public void RemoveProject(Guid projectId)
        {
            var project = _projects.FirstOrDefault(p => p.Id == projectId);
            if (project != null)
            {
                _projects.Remove(project);
                SaveProjectConfigurations();
                Logger.Info($"删除项目: {project.Name}");
            }
        }

        /// <summary>
        /// 根据ID获取项目配置
        /// </summary>
        public ProjectConfig? GetProject(Guid projectId)
        {
            return _projects.FirstOrDefault(p => p.Id == projectId);
        }

        /// <summary>
        /// 设置当前活动项目
        /// </summary>
        public void SetActiveProject(Guid projectId)
        {
            _appConfig.LastActiveProjectId = projectId;
            SaveAppConfiguration();
        }

        /// <summary>
        /// 获取当前活动项目
        /// </summary>
        public ProjectConfig? GetActiveProject()
        {
            if (_appConfig.LastActiveProjectId == Guid.Empty)
                return _projects.FirstOrDefault();
            
            return GetProject(_appConfig.LastActiveProjectId) ?? _projects.FirstOrDefault();
        }

        private void LoadProjectConfigurations()
        {
            if (!File.Exists(_projectConfigPath))
            {
                _projects = new List<ProjectConfig>();
                Logger.Info("项目配置文件不存在，创建新的配置");
                return;
            }

            try
            {
                var json = File.ReadAllText(_projectConfigPath);
                _projects = JsonConvert.DeserializeObject<List<ProjectConfig>>(json) ?? new List<ProjectConfig>();
                Logger.Info($"加载了 {_projects.Count} 个项目配置");
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "解析项目配置文件时发生错误，使用默认配置");
                _projects = new List<ProjectConfig>();
            }
        }

        private void LoadAppConfiguration()
        {
            if (!File.Exists(_appConfigPath))
            {
                _appConfig = new AppConfig();
                Logger.Info("应用程序配置文件不存在，创建新的配置");
                return;
            }

            try
            {
                var json = File.ReadAllText(_appConfigPath);
                _appConfig = JsonConvert.DeserializeObject<AppConfig>(json) ?? new AppConfig();
                Logger.Info("应用程序配置加载成功");
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "解析应用程序配置文件时发生错误，使用默认配置");
                _appConfig = new AppConfig();
            }
        }

        private void EnsureConfigDirectoryExists()
        {
            if (!Directory.Exists(_configDirectory))
            {
                Directory.CreateDirectory(_configDirectory);
                Logger.Info($"创建配置目录: {_configDirectory}");
            }
        }
    }

    /// <summary>
    /// 应用程序配置
    /// </summary>
    public class AppConfig
    {
        /// <summary>
        /// 最后活动的项目ID
        /// </summary>
        public Guid LastActiveProjectId { get; set; } = Guid.Empty;

        /// <summary>
        /// VS2008安装路径
        /// </summary>
        public string VS2008Path { get; set; } = @"C:\Program Files (x86)\Microsoft Visual Studio 9.0\Common7\IDE\devenv.exe";

        /// <summary>
        /// 窗口位置和大小
        /// </summary>
        public WindowSettings WindowSettings { get; set; } = new WindowSettings();

        /// <summary>
        /// 自动保存间隔（分钟）
        /// </summary>
        public int AutoSaveInterval { get; set; } = 5;

        /// <summary>
        /// 是否启用自动保存
        /// </summary>
        public bool EnableAutoSave { get; set; } = true;

        /// <summary>
        /// 日志级别
        /// </summary>
        public string LogLevel { get; set; } = "Info";
    }

    /// <summary>
    /// 窗口设置
    /// </summary>
    public class WindowSettings
    {
        public double Left { get; set; } = 100;
        public double Top { get; set; } = 100;
        public double Width { get; set; } = 1000;
        public double Height { get; set; } = 700;
        public bool IsMaximized { get; set; } = false;
    }
}
