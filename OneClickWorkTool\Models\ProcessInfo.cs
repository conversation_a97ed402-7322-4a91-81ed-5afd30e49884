using System;
using System.ComponentModel;
using System.Diagnostics;
using System.Runtime.CompilerServices;

namespace OneClickWorkTool.Models
{
    /// <summary>
    /// 进程信息模型
    /// </summary>
    public class ProcessInfo : INotifyPropertyChanged
    {
        private string _name = string.Empty;
        private int _processId;
        private ProcessStatus _status = ProcessStatus.Stopped;
        private DateTime _startTime;
        private TimeSpan _runningTime;
        private long _memoryUsage;
        private double _cpuUsage;

        /// <summary>
        /// 进程名称
        /// </summary>
        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        /// <summary>
        /// 进程ID
        /// </summary>
        public int ProcessId
        {
            get => _processId;
            set => SetProperty(ref _processId, value);
        }

        /// <summary>
        /// 进程状态
        /// </summary>
        public ProcessStatus Status
        {
            get => _status;
            set => SetProperty(ref _status, value);
        }

        /// <summary>
        /// 启动时间
        /// </summary>
        public DateTime StartTime
        {
            get => _startTime;
            set => SetProperty(ref _startTime, value);
        }

        /// <summary>
        /// 运行时长
        /// </summary>
        public TimeSpan RunningTime
        {
            get => _runningTime;
            set => SetProperty(ref _runningTime, value);
        }

        /// <summary>
        /// 内存使用量（字节）
        /// </summary>
        public long MemoryUsage
        {
            get => _memoryUsage;
            set => SetProperty(ref _memoryUsage, value);
        }

        /// <summary>
        /// CPU使用率（百分比）
        /// </summary>
        public double CpuUsage
        {
            get => _cpuUsage;
            set => SetProperty(ref _cpuUsage, value);
        }

        /// <summary>
        /// 可执行文件路径
        /// </summary>
        public string ExecutablePath { get; set; } = string.Empty;

        /// <summary>
        /// 启动参数
        /// </summary>
        public string StartupArgs { get; set; } = string.Empty;

        /// <summary>
        /// 工作目录
        /// </summary>
        public string WorkingDirectory { get; set; } = string.Empty;

        /// <summary>
        /// 关闭脚本路径
        /// </summary>
        public string CloseScriptPath { get; set; } = string.Empty;

        /// <summary>
        /// 进程类型
        /// </summary>
        public ProcessType Type { get; set; } = ProcessType.Frontend;

        /// <summary>
        /// 关联的Process对象
        /// </summary>
        public Process? Process { get; set; }

        /// <summary>
        /// 内存使用量显示文本
        /// </summary>
        public string MemoryUsageText
        {
            get
            {
                if (MemoryUsage < 1024 * 1024)
                    return $"{MemoryUsage / 1024:F1} KB";
                else if (MemoryUsage < 1024 * 1024 * 1024)
                    return $"{MemoryUsage / (1024 * 1024):F1} MB";
                else
                    return $"{MemoryUsage / (1024 * 1024 * 1024):F1} GB";
            }
        }

        /// <summary>
        /// 运行时长显示文本
        /// </summary>
        public string RunningTimeText
        {
            get
            {
                if (Status != ProcessStatus.Running)
                    return "未运行";
                
                var time = DateTime.Now - StartTime;
                if (time.TotalDays >= 1)
                    return $"{(int)time.TotalDays}天 {time.Hours:D2}:{time.Minutes:D2}:{time.Seconds:D2}";
                else
                    return $"{time.Hours:D2}:{time.Minutes:D2}:{time.Seconds:D2}";
            }
        }

        /// <summary>
        /// 状态显示文本
        /// </summary>
        public string StatusText
        {
            get
            {
                return Status switch
                {
                    ProcessStatus.Running => "运行中",
                    ProcessStatus.Stopped => "已停止",
                    ProcessStatus.Starting => "启动中",
                    ProcessStatus.Stopping => "停止中",
                    ProcessStatus.Error => "错误",
                    _ => "未知"
                };
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            
            // 当状态或其他相关属性改变时，通知相关的显示属性
            if (propertyName == nameof(Status))
            {
                OnPropertyChanged(nameof(StatusText));
                OnPropertyChanged(nameof(RunningTimeText));
            }
            else if (propertyName == nameof(MemoryUsage))
            {
                OnPropertyChanged(nameof(MemoryUsageText));
            }
            else if (propertyName == nameof(StartTime))
            {
                OnPropertyChanged(nameof(RunningTimeText));
            }
            
            return true;
        }
    }

    /// <summary>
    /// 进程状态枚举
    /// </summary>
    public enum ProcessStatus
    {
        /// <summary>
        /// 已停止
        /// </summary>
        Stopped,
        
        /// <summary>
        /// 启动中
        /// </summary>
        Starting,
        
        /// <summary>
        /// 运行中
        /// </summary>
        Running,
        
        /// <summary>
        /// 停止中
        /// </summary>
        Stopping,
        
        /// <summary>
        /// 错误状态
        /// </summary>
        Error
    }

    /// <summary>
    /// 进程类型枚举
    /// </summary>
    public enum ProcessType
    {
        /// <summary>
        /// 前端进程
        /// </summary>
        Frontend,
        
        /// <summary>
        /// 后端进程
        /// </summary>
        Backend
    }
}
